//
//  DiscoverViewController.swift
//  Shuxiaoqi
//
//  Created by yongsheng ye on 2025/3/19.
//
//  首页-发现页

import UIKit
import JXPagingView
import JXSegmentedView
import MJRefresh

// MARK: - 下拉刷新协议
protocol ChannelRefreshable: AnyObject {
    func reloadData(completion: @escaping () -> Void)
}

// 发现页面控制器
class DiscoverViewController: BaseViewController {
    
    // 图标菜单集合视图
    private var iconMenuCollectionView: UICollectionView!
    
    // 页面控制器 (用于旧的翻页指示器，在组合布局中不再直接使用，但自定义指示器逻辑可能需要类似的计算)
    private var pageControl: UIPageControl!
    
    // 图标数据
    private var iconMenuItems: [AppHomeConfigResponseData] = []
    
    // 页面指示器容器视图
    private var pageIndicatorContainer: UIView!
    
    // 分段视图 (用于展示分类菜单)
    private var segmentedView: JXSegmentedView!
    // 分段视图的数据源
    private var segmentedDataSource: JXSegmentedTitleDataSource!
    // 列表容器视图 (用于承载不同分类下的列表)
    private var listContainerView: JXSegmentedListContainerView!
    // 下拉按钮 (用于展开分类选择弹窗)
    private var dropdownButton: UIButton!
    
    // 分类选择弹窗视图
    private var categoryPopupView: CategorySelectionPopupView?
    // 蒙版视图 (用于弹窗背景)
    private var overlayView: UIView?
    
    // 菜单数据 - 包含id和name的元组数组
    private var menuItems: [(id: Int, name: String)] = [
        (id: 0, name: "推荐"), // 默认推荐分类的ID为0
        (id: 1, name: "直播")
    ]
    
    override func viewDidLoad() {
        super.viewDidLoad()
        
        showNavBar = false
        
        self.isTabBarRootViewController = false // 标记为TabBar的根视图控制器
        view.backgroundColor = .appBackgroundGray // 设置背景色
        
        // 设置图标菜单
        setupIconMenu()
        
        // 获取分段列表数据 (通常是异步获取，完成后更新UI)
        loadSegmentedListData()
        
        // 设置分段菜单
        setupSegmentedMenu()
    }
    
    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
        
        // 打印日志，表明发现页已展示
        print("发现页")
    }
    
    // MARK: - 图标菜单设置
    // 设置图标菜单 (入口方法)
    private func setupIconMenu() {
        if iconMenuCollectionView == nil {
            setupIconMenuWithCompositionalLayout()
        }
        APIManager.shared.getAppHomeConfigList(type: 0) { [weak self] result in
            guard let self = self else { return }
            DispatchQueue.main.async {
                switch result {
                case .success(let response):
                    if let items = response.data, !items.isEmpty {
                        // 按sort从小到大排序
                        let sortedItems = items.sorted { ($0.sort ?? 0) < ($1.sort ?? 0) }
                        self.iconMenuItems = sortedItems
                        print("AppHomeConfig: 菜单数据加载成功，共\(sortedItems.count)项，已按sort升序排序")

                        // 调试：打印每个菜单项的解析结果
                        for (index, item) in sortedItems.enumerated() {
                            print("菜单项 \(index): \(item.label ?? "未知")")
                            print("  - toAddress: \(item.toAddress ?? "nil")")
                            print("  - jumpType: \(item.jumpType)")
                            print("  - jumpUrl: \(item.jumpUrl)")
                        }
                    } else {
                        self.iconMenuItems = []
                        self.showToast("未获取到菜单数据")
                        print("AppHomeConfig: 数据为空或无效")
                    }
                case .failure(let error):
                    self.iconMenuItems = []
                    self.showToast(error.errorMessage)
                    print("AppHomeConfig: 获取失败 - \(error.errorMessage)")
                }
                self.iconMenuCollectionView?.reloadData()
                // 动态创建或移除指示器
                if self.iconMenuItems.count > 5 {
                    if self.pageIndicatorContainer == nil {
                        self.setupPageIndicator()
                    }
                } else {
                    // 如果不需要指示器，移除并置nil
                    self.pageIndicatorContainer?.removeFromSuperview()
                    self.pageIndicatorContainer = nil
                }
            }
        }
    }
    
    // 使用 UICollectionViewCompositionalLayout 设置图标菜单
    private func setupIconMenuWithCompositionalLayout() {
        // 创建布局
        let layout = createCompositionalLayout()
        
        // 创建集合视图
        let screenWidth = UIScreen.main.bounds.width
        // 定义 iconMenuCollectionView 的初始位置和大小
        iconMenuCollectionView = UICollectionView(frame: CGRect(x: 0, y: 8, width: screenWidth, height: 64), collectionViewLayout: layout)
        iconMenuCollectionView.backgroundColor = .appBackgroundGray
        iconMenuCollectionView.showsHorizontalScrollIndicator = false // 不显示水平滚动条
        
        // 设置代理和数据源
        iconMenuCollectionView.delegate = self
        iconMenuCollectionView.dataSource = self
        
        // 注册单元格
        iconMenuCollectionView.register(IconMenuCell.self, forCellWithReuseIdentifier: "IconMenuCell")
        
        // 添加到视图
        contentView.addSubview(iconMenuCollectionView)
        
        // 只有当图标总数超过每页显示数量(5个)时，才添加翻页指示器
        if iconMenuItems.count > 5 {
            setupPageIndicator() // 设置自定义翻页指示器
        }
    }
    
    // MARK: - 分段列表数据加载
    // 加载分段列表数据 (从API获取或使用默认数据)
    private func loadSegmentedListData() {
        APIManager.shared.getAppHomeConfigList(type: 1) { [weak self] result in
            guard let self = self else { return }

            DispatchQueue.main.async {
                // 默认分类（接口无数据时兜底）
                let defaultSecondaryCategories: [(id: Int, name: String)] = [
                    (id: 0, name: "推荐"),
                    (id: 1, name: "直播"), (id: 2, name: "穿搭"),
                    (id: 3, name: "摄影"), (id: 4, name: "美食"),
                    (id: 5, name: "彩妆"), (id: 6, name: "职业")
                ]

                var fetchedItems: [(id: Int, name: String, sort: Int)] = []

                switch result {
                case .success(let response):
                    if let data = response.data, !data.isEmpty {
                        // 解析接口数据，使用 toAddress 中的 url 作为分类ID
                        for item in data {
                            guard let name = item.label,
                                  !name.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty
                            else { continue }

                            // 打印原始数据用于调试
                            print("🔍 解析分类数据: \(name)")
                            print("  - 原始id: \(item.id ?? -1)")
                            print("  - toAddress: \(item.toAddress ?? "nil")")
                            print("  - jumpUrl: '\(item.jumpUrl)'")

                            // 从 toAddress 中解析出真正的分类ID
                            let categoryId: Int
                            let urlString = item.jumpUrl
                            if !urlString.isEmpty, let urlInt = Int(urlString) {
                                categoryId = urlInt
                                print("  ✅ 成功解析分类ID: \(categoryId)")
                            } else {
                                // 如果解析失败，使用原始id作为兜底
                                categoryId = item.id ?? 0
                                print("  ❌ 无法从toAddress解析分类ID，使用原始id: \(categoryId), jumpUrl: '\(urlString)'")
                            }

                            let sort = item.sort ?? 0
                            fetchedItems.append((id: categoryId, name: name, sort: sort))
                        }
                    } else {
                        print("getAppHomeConfigList success but no valid data: \(response.displayMessage)")
                    }
                case .failure(let error):
                    print("getAppHomeConfigList failed: \(error.localizedDescription)")
                }

                // 根据 sort 字段排序
                fetchedItems.sort { $0.sort < $1.sort }

                // 转换为 (id, name) 元组数组
                var newMenuItems: [(id: Int, name: String)] = fetchedItems.map { ($0.id, $0.name) }

                // 如果包含 "推荐"，确保其位于第一位
                if let recommendIndex = newMenuItems.firstIndex(where: { $0.name == "推荐" }) {
                    let recommendItem = newMenuItems.remove(at: recommendIndex)
                    newMenuItems.insert(recommendItem, at: 0)
                }

                // 若接口无有效数据，则使用默认分类
                if newMenuItems.isEmpty {
                    newMenuItems = defaultSecondaryCategories
                }

                // 更新 UI
                self.menuItems = newMenuItems
                self.segmentedDataSource.titles = newMenuItems.map { $0.name }
                self.segmentedView.reloadData()
                self.listContainerView.reloadData()

                // 打印调试信息
                print("分类数据加载完成:")
                for item in newMenuItems {
                    print("  - \(item.name): categoryId = \(item.id)")
                }
            }
        }
    }
    
    // MARK: - 组合布局创建
    // 创建 UICollectionViewCompositionalLayout 布局
    private func createCompositionalLayout() -> UICollectionViewLayout {
        // 创建一个组合布局
        let layout = UICollectionViewCompositionalLayout { [weak self] (sectionIndex, layoutEnvironment) -> NSCollectionLayoutSection? in
            guard let self = self else { return nil }
            
            // 计算每个图标的宽度和间距
            let screenWidth = UIScreen.main.bounds.width
            let itemsPerPage = 5 // 每页显示的图标数量
            let sideInset: CGFloat = 24 // 两边的内边距
            let availableWidth = screenWidth - (sideInset * 2) // 可用于图标和间距的总宽度
            let itemWidth: CGFloat = 64 // 每个图标的固定宽度
            
            // 计算间距，使5个图标在可用宽度内均匀分布
            let totalSpacing = availableWidth - (itemWidth * CGFloat(itemsPerPage))
            let spacing = totalSpacing / CGFloat(itemsPerPage - 1) // 图标之间的间距
            
            // 创建项目 (item) 大小
            let itemSize = NSCollectionLayoutSize(widthDimension: .absolute(itemWidth), heightDimension: .absolute(64))
            let item = NSCollectionLayoutItem(layoutSize: itemSize)
            
            // 创建组 (group) 大小
            let groupSize = NSCollectionLayoutSize(widthDimension: .absolute(screenWidth), heightDimension: .absolute(64))
            
            // 创建组 - 水平排列，包含指定数量的项目
            let group = NSCollectionLayoutGroup.horizontal(layoutSize: groupSize, subitem: item, count: itemsPerPage)
            group.interItemSpacing = .fixed(spacing) // 设置项目间的固定间距
            group.contentInsets = NSDirectionalEdgeInsets(top: 0, leading: sideInset, bottom: 0, trailing: sideInset) // 设置组的内边距
            
            // 创建节 (section)
            let section = NSCollectionLayoutSection(group: group)
            section.contentInsets = .zero // 节的内边距设为0
            
            // 设置正交滚动行为，实现分页效果
            section.orthogonalScrollingBehavior = .groupPagingCentered
            
            // 添加可见项目变化的回调，用于更新页面指示器
            section.visibleItemsInvalidationHandler = { [weak self] (items, offset, environment) in
                guard let self = self else { return }
                let pageWidth = environment.container.contentSize.width // 每页的宽度
                let currentPage = Int(round(offset.x / pageWidth)) // 计算当前页码
                if self.iconMenuItems.count > 5 { // 仅当图标数大于5时更新指示器
                    self.updatePageIndicator(currentPage: currentPage) // 更新自定义页面指示器
                }
            }
            
            return section
        }
        
        return layout
    }
    
    // scrollViewWillEndDragging 和 scrollViewDidEndDecelerating 方法可以被移除，
    // 因为组合布局的 visibleItemsInvalidationHandler 已经用于更新指示器。
    
    // MARK: - 页面指示器操作
    // 处理页面指示器的点击手势
    @objc private func handlePageIndicatorTap(_ gesture: UITapGestureRecognizer) {
        guard let pageIndicatorContainer = self.pageIndicatorContainer else { return }
        let location = gesture.location(in: pageIndicatorContainer) // 获取点击位置
        let pageCount = Int(ceil(Double(iconMenuItems.count) / 5.0)) // 计算总页数
        
        for i in 0..<pageCount {
            if let indicator = pageIndicatorContainer.viewWithTag(1000 + i) { // 通过tag找到对应的指示器点
                if indicator.frame.contains(location) { // 判断点击位置是否在指示器点内
                    // 滚动到对应页面
                    let indexPath = IndexPath(item: i * 5, section: 0) // 计算目标页的第一个item的indexPath
                    iconMenuCollectionView.scrollToItem(at: indexPath, at: .left, animated: true) // 滚动CollectionView
                    
                    // 更新指示器状态
                    updatePageIndicator(currentPage: i)
                    break // 找到后即跳出循环
                }
            }
        }
    }
    
    // viewDidLayoutSubviews 方法可以被移除或简化，
    // 因为使用 UICollectionViewCompositionalLayout 时，通常不需要手动调整内容大小。
    override func viewDidLayoutSubviews() {
        super.viewDidLayoutSubviews()
        
        // 动态调整所有子视图frame以适配contentView
        let screenWidth = contentView.bounds.width
        iconMenuCollectionView?.frame = CGRect(x: 0, y: 8, width: screenWidth, height: 64)
        let pageIndicatorHeight: CGFloat = 10
        pageIndicatorContainer?.frame = CGRect(x: 0, y: 8 + 64 + 4, width: screenWidth, height: pageIndicatorHeight)
        let menuY: CGFloat = 8 + 64 + 4 + pageIndicatorHeight + 4
        segmentedView?.frame = CGRect(x: 0, y: menuY, width: screenWidth - 48, height: 44)
        dropdownButton?.frame = CGRect(x: screenWidth - 40 - 8, y: menuY, width: 32, height: 44)
        let containerY = menuY + 49
        let containerHeight = contentView.bounds.height - containerY
        if listContainerView?.frame.height != containerHeight {
            print("containerHeight: \(containerHeight)")
        }
        listContainerView?.frame = CGRect(x: 0, y: containerY, width: screenWidth, height: containerHeight)
    }
    
    // MARK: - 页面指示器设置
    // 设置自定义页面指示器
    private func setupPageIndicator() {
        let screenWidth = UIScreen.main.bounds.width
        let pageCount = Int(ceil(Double(iconMenuItems.count) / 5.0)) // 总页数
        
        // 创建自定义页面指示器容器视图
        let pageIndicatorContainer = UIView()
        let pageIndicatorHeight: CGFloat = 10 // 指示器容器高度
        // 设置指示器容器的位置 (位于图标菜单下方，有4pt间距)
        pageIndicatorContainer.frame = CGRect(x: 0, y: 8 + 64 + 4, width: screenWidth, height: pageIndicatorHeight)
        
        // 指示器点的参数
        let indicatorWidth: CGFloat = 12 // 单个指示器点的宽度
        let indicatorHeight: CGFloat = 4  // 单个指示器点的高度
        let indicatorSpacing: CGFloat = 6 // 指示器点之间的间距
        let totalWidth = CGFloat(pageCount) * indicatorWidth + CGFloat(pageCount - 1) * indicatorSpacing // 所有指示器点和间距的总宽度
        let startX = (screenWidth - totalWidth) / 2 // 第一个指示器点的起始X坐标，使其居中显示
        
        // 创建并添加所有指示器点
        for i in 0..<pageCount {
            let indicator = UIView()
            indicator.frame = CGRect(
                x: startX + CGFloat(i) * (indicatorWidth + indicatorSpacing),
                y: (pageIndicatorHeight - indicatorHeight) / 2, // 使指示器点在容器中垂直居中
                width: indicatorWidth,
                height: indicatorHeight
            )
            // 设置当前页指示器的颜色和非当前页指示器的颜色
            indicator.backgroundColor = i == 0 ? UIColor(hex: "#FF8D36") : UIColor.lightGray.withAlphaComponent(0.3)
            indicator.layer.cornerRadius = indicatorHeight / 2 // 设置圆角
            indicator.tag = 1000 + i // 设置tag用于后续识别和更新
            
            pageIndicatorContainer.addSubview(indicator)
        }
        
        // 添加到主视图
        contentView.addSubview(pageIndicatorContainer)
        
        // 保存对指示器容器的引用
        self.pageIndicatorContainer = pageIndicatorContainer
        
        // 为指示器容器添加点击手 Règles (处理点击切换页面)
        let tapGesture = UITapGestureRecognizer(target: self, action: #selector(handlePageIndicatorTap(_:)))
        pageIndicatorContainer.addGestureRecognizer(tapGesture)
        pageIndicatorContainer.isUserInteractionEnabled = true // 启用用户交互
    }
    
    // 更新页面指示器的状态 (颜色)
    private func updatePageIndicator(currentPage: Int) {
        guard let pageIndicatorContainer = self.pageIndicatorContainer else { return }
        let pageCount = Int(ceil(Double(iconMenuItems.count) / 5.0)) // 总页数
        
        for i in 0..<pageCount {
            if let indicator = pageIndicatorContainer.viewWithTag(1000 + i) { // 通过tag找到指示器点
                // 根据是否为当前页，更新指示器点的背景色
                indicator.backgroundColor = i == currentPage ? UIColor(hex: "#FF8D36") : UIColor.lightGray.withAlphaComponent(0.3)
            }
        }
    }
    
    // MARK: - 分段菜单设置
    // 设置分段菜单 (JXSegmentedView)
    private func setupSegmentedMenu() {
        // 创建下拉按钮 (与分段菜单并排显示)
        setupDropdownButton()
        
        // 计算分段菜单的Y坐标 (位于页面指示器下方，有额外4pt间距)
        let pageIndicatorHeight: CGFloat = 10 // 页面指示器的高度
        let menuY: CGFloat = 8 + 64 + 4 + pageIndicatorHeight + 4 // 图标菜单高度 + 图标菜单下方间距 + 页面指示器高度 + 额外4pt间距
        
        // 创建分段视图 (宽度为屏幕宽度减去下拉按钮的宽度和一些边距)
        segmentedView = JXSegmentedView(frame: CGRect(x: 0, y: menuY, width: contentView.bounds.width - 48, height: 44))
        segmentedView.backgroundColor = .appBackgroundGray
        
        // 配置数据源 (JXSegmentedTitleDataSource)
        segmentedDataSource = JXSegmentedTitleDataSource()
        segmentedDataSource.titles = menuItems.map { $0.name } // 设置标题为 menuItems 中的 name
        segmentedDataSource.titleNormalColor = UIColor(hex: "#333333") // 普通状态标题颜色
        segmentedDataSource.titleSelectedColor = UIColor(hex: "#FB6C04") // 选中状态标题颜色
        segmentedDataSource.titleNormalFont = .systemFont(ofSize: 16) // 普通状态标题字体
        segmentedDataSource.titleSelectedFont = .boldSystemFont(ofSize: 16) // 选中状态标题字体 (加粗)
        segmentedDataSource.isTitleColorGradientEnabled = true // 启用标题颜色渐变
        segmentedDataSource.itemSpacing = 23 // 设置标题之间的间距，默认为20，增加4pt
        segmentedView.dataSource = segmentedDataSource
        
        // 配置指示器 (点状指示器)
        let indicator = JXSegmentedIndicatorDotLineView()
        indicator.indicatorWidth = 4 // 点的宽度
        indicator.indicatorHeight = 4 // 点的高度
        indicator.indicatorColor = UIColor(hex: "#FB6C04") // 点的颜色
        indicator.verticalOffset = 2 // 垂直偏移量 (调整点的位置)
        segmentedView.indicators = [indicator]
        
        // 设置代理
        segmentedView.delegate = self
        
        // 添加到视图
        contentView.addSubview(segmentedView)
        
        // 创建内容容器视图 (JXSegmentedListContainerView)
        let containerY = menuY + 49 // 容器的Y坐标 (位于分段菜单下方) +5 的间隔
        
        // 计算容器高度，确保内容区域不与 TabBar 和底部安全区域重叠
        let containerHeight = contentView.bounds.height - containerY
        if listContainerView?.frame.height != containerHeight {
            print("containerHeight: \(containerHeight)")
        }
        // 创建列表容器
        listContainerView = JXSegmentedListContainerView(dataSource: self)
        listContainerView.frame = CGRect(x: 0, y: containerY, width: contentView.bounds.width, height: containerHeight)
        contentView.addSubview(listContainerView)
        
        // 关联 segmentedView 和 listContainerView，实现联动效果
        segmentedView.listContainer = listContainerView
    }
    
    // MARK: - 下拉按钮设置
    // 设置下拉按钮 (用于展开分类选择)
    private func setupDropdownButton() {
        // 创建按钮实例
        dropdownButton = UIButton(type: .custom)
        
        // 计算按钮的Y坐标 (与分段菜单的Y坐标保持一致)
        let pageIndicatorHeight: CGFloat = 10 // 页面指示器的高度
        let buttonY: CGFloat = 8 + 64 + 4 + pageIndicatorHeight + 4 // 与分段菜单相同的Y坐标
        
        // 设置按钮的frame (初始位置和大小)
        dropdownButton.frame = CGRect(x: contentView.bounds.width - 40, y: buttonY, width: 32, height: 44)
        
        // 设置按钮图标
        dropdownButton.setImage(UIImage(named: "home_down_arrow"), for: .normal)
        // 添加点击事件
        dropdownButton.addTarget(self, action: #selector(dropdownButtonTapped), for: .touchUpInside)
        
        // 调整按钮位置，使其右边距为8pt
        dropdownButton.frame.origin.x = contentView.bounds.width - dropdownButton.frame.width - 8
        
        contentView.addSubview(dropdownButton)
    }
    
    // MARK: - 下拉按钮点击与弹窗处理
    // 处理下拉按钮点击事件，显示或隐藏分类选择弹窗
    @objc private func dropdownButtonTapped() {
        // 处理下拉按钮点击事件
        print("下拉按钮被点击")
        
        if categoryPopupView != nil {
            dismissCategoryPopup() // 如果弹窗已显示，则关闭它
            return
        }

        let popupItems = self.menuItems // 获取用于弹窗的菜单项
        if popupItems.isEmpty { return } // 如果没有菜单项，则不执行任何操作

        // 1. 创建蒙版 (透明背景，点击可关闭弹窗)
        overlayView = UIView(frame: self.view.bounds)
        overlayView?.backgroundColor = .clear // 设置蒙版为透明
        overlayView?.alpha = 0 // 初始状态为隐藏 (通过动画显示)
        let tapGesture = UITapGestureRecognizer(target: self, action: #selector(dismissCategoryPopup))
        overlayView?.addGestureRecognizer(tapGesture) // 为蒙版添加点击手势以关闭弹窗
        self.view.addSubview(overlayView!)
        // 可选: 将蒙版置于顶层，但在弹窗之后（如果弹窗稍后添加）
        // self.view.bringSubviewToFront(overlayView!)

        // 2. 创建弹窗视图 (CategorySelectionPopupView)
        guard segmentedView.frame != .zero else {
            // 如果分段视图的frame尚未确定，则无法正确定位弹窗，打印错误并返回
            print("SegmentedView frame is not yet set.") // 分段视图的frame尚未设置
            overlayView?.removeFromSuperview() // 移除已添加的蒙版
            overlayView = nil
            return
        }
        
        // 使用 segmentedView 的高度作为弹窗头部的高度 (弹窗头部将覆盖segmentedView)
        let popupHeaderHeight = segmentedView.frame.height
        let popupOriginY = segmentedView.frame.origin.y // 弹窗顶部与分段视图顶部对齐
        let popupWidth = contentView.bounds.width // 弹窗宽度与屏幕同宽
        
        // 定义弹窗内 collectionView 的布局参数
        let itemsPerRow = 4 // 每行显示的选项数量
        let itemCellHeight: CGFloat = 44 // 每个选项单元格的高度
        let lineSpacing: CGFloat = 8 // 行间距
        let verticalPaddingInPopupCollectionView: CGFloat = 12 * 2 // 弹窗内 collectionView 的垂直总内边距

        // 根据项目数量和布局参数计算 collectionView 的实际需要高度
        let numberOfRows = ceil(Double(popupItems.count) / Double(itemsPerRow))
        let calculatedCollectionViewHeight = (CGFloat(numberOfRows) * itemCellHeight) + (CGFloat(max(0, numberOfRows - 1)) * lineSpacing) + verticalPaddingInPopupCollectionView
        
        // 初始frame：弹窗收起状态，只有头部可见，刚好覆盖分段视图
        let initialFrame = CGRect(x: 0, y: popupOriginY, width: popupWidth, height: popupHeaderHeight)
        // 目标frame：弹窗展开状态，包含头部和展开的 collectionView
        let targetFrame = CGRect(x: 0, y: popupOriginY, width: popupWidth, height: popupHeaderHeight + calculatedCollectionViewHeight)

        // 创建分类选择弹窗视图实例
        categoryPopupView = CategorySelectionPopupView(frame: initialFrame, items: popupItems, itemsPerRow: itemsPerRow, itemHeight: itemCellHeight, headerHeight: popupHeaderHeight)
        categoryPopupView?.delegate = self // 设置代理以处理弹窗内的选择和关闭请求
        
        self.view.addSubview(categoryPopupView!) // 将弹窗添加到视图层级 (应在蒙版之上)
        // 可选: 确保弹窗在最顶层
        // self.view.bringSubviewToFront(categoryPopupView!)

        // 3. 动画显示弹窗
        // 蒙版立即显示，弹窗从收起状态 (仅头部) 动画展开到完整高度
        self.overlayView?.alpha = 1 // 立即显示蒙版 (使其可见)
        UIView.animate(withDuration: 0.3, delay: 0, options: .curveEaseOut, animations: {
            self.categoryPopupView?.frame = targetFrame // 执行弹窗展开动画
        })
    }
    
    // 关闭分类选择弹窗 (带动画)
    @objc private func dismissCategoryPopup() {
        guard let popup = categoryPopupView, let overlay = overlayView else { return } // 确保弹窗和蒙版都存在

        // 关闭动画的目标frame：弹窗收起至只有头部可见
        let popupHeaderHeight = segmentedView.frame.height // 获取头部高度 (与分段视图同高)
        let popupOriginY = segmentedView.frame.origin.y // 获取Y坐标 (与分段视图同高)
        let targetFrame = CGRect(x: 0, y: popupOriginY, width: popup.frame.width, height: popupHeaderHeight)

        // 执行动画：蒙版渐隐，弹窗收起
        UIView.animate(withDuration: 0.3, delay: 0, options: .curveEaseIn, animations: {
            overlay.alpha = 0 // 渐隐蒙版
            popup.frame = targetFrame // 执行弹窗收起动画
        }, completion: { _ in
            // 动画完成后，从父视图移除并清空引用
            popup.removeFromSuperview() // 移除弹窗
            overlay.removeFromSuperview() // 移除蒙版
            self.categoryPopupView = nil // 清空弹窗引用
            self.overlayView = nil // 清空蒙版引用
        })
    }

    // MARK: - 首页 Tab 双击刷新处理
    /// 将所有可滚动视图和数据重置到初始状态
    func resetContentToTop() {
        print("DiscoverViewController: 执行 resetContentToTop")

        // 1. 将图标菜单滚动回第一页 (使用 scrollToItem 保证分页效果正确)
        if iconMenuItems.count > 0 {
            let firstIndexPath = IndexPath(item: 0, section: 0)
            iconMenuCollectionView.scrollToItem(at: firstIndexPath, at: .left, animated: true)
        } else {
            iconMenuCollectionView.setContentOffset(.zero, animated: true)
        }
        updatePageIndicator(currentPage: 0)

        // 2. 分段菜单切回第一项
        segmentedView.selectItemAt(index: 0)

        // 3. 遍历已经加载的列表，执行刷新并滚动到顶部
        for list in listContainerView.validListDict.values {
            if let rec = list as? RecommendListViewController {
                // 取消内容刷新，仅执行滚动到顶部及横滑列表复位
                rec.scrollToTop()
            } else if let refreshable = list as? ChannelRefreshable {
                // 暂时关闭其他频道的内容刷新，仅执行滚动复位逻辑（如有）
                if let vc = refreshable as? UIViewController,
                   let scrollView = vc.view as? UIScrollView {
                    scrollView.setContentOffset(.zero, animated: true)
                }
            } else if let scrollView = (list as? UIViewController)?.view as? UIScrollView {
                scrollView.setContentOffset(.zero, animated: true)
            }
        }
    }
}

// MARK: - UICollectionViewDelegate, UICollectionViewDataSource
// MARK: - UICollectionView 代理和数据源 (用于图标菜单)
extension DiscoverViewController: UICollectionViewDelegate, UICollectionViewDataSource {
    func collectionView(_ collectionView: UICollectionView, numberOfItemsInSection section: Int) -> Int {
        let totalItems = iconMenuItems.count
        let pageSize = 5 // 每页显示5个图标
        let numberOfPages = Int(ceil(Double(totalItems) / Double(pageSize)))
        return numberOfPages * pageSize
    }

    func collectionView(_ collectionView: UICollectionView, cellForItemAt indexPath: IndexPath) -> UICollectionViewCell {
        let cell = collectionView.dequeueReusableCell(withReuseIdentifier: "IconMenuCell", for: indexPath) as! IconMenuCell
        if indexPath.item < iconMenuItems.count {
            let config = iconMenuItems[indexPath.item]
            cell.configure(with: config.icon ?? "")
            cell.isHidden = false
        } else {
            cell.isHidden = true
        }
        return cell
    }

    func collectionView(_ collectionView: UICollectionView, didSelectItemAt indexPath: IndexPath) {
        if indexPath.item < iconMenuItems.count {
            let config = iconMenuItems[indexPath.item]

            // 打印调试信息
            print("=== 菜单点击调试信息 ===")
            print("菜单名称: \(config.label ?? "未知")")
            print("原始toAddress: \(config.toAddress ?? "nil")")
            print("解析后的jumpType: \(config.jumpType)")
            print("解析后的jumpUrl: \(config.jumpUrl)")

            // 获取跳转URL和类型
            let jumpUrl = config.jumpUrl
            guard !jumpUrl.isEmpty else {
                print("❌ jumpUrl为空，无法跳转")
                showToast("跳转链接无效")
                return
            }

            let jumpType = config.jumpType
            let menuName = config.label ?? "菜单"

            // 根据跳转类型处理
            switch jumpType {
            case "webView":
                print("✅ 准备跳转到WebView: \(jumpUrl)")
                if let url = URL(string: jumpUrl) {
                    let webVC = WebViewController(url: url, title: menuName)
                    self.navigationController?.pushViewController(webVC, animated: true)
                    print("✅ WebViewController已创建并推送")
                } else {
                    print("❌ URL格式无效: \(jumpUrl)")
                    showToast("链接格式错误")
                }
            case "weChatProgram":
                print("✅ 准备跳转到微信小程序: \(jumpUrl)")
                // 处理微信小程序跳转
                handleWeChatMiniProgramJump(userName: jumpUrl, menuName: menuName)
            default:
                print("❌ 未知的跳转类型: \(jumpType)")
                showToast("暂不支持此类型跳转")
            }
        } else {
            print("❌ 点击了无效的图标菜单项 (填充项)")
        }
    }
}


// MARK: - JXSegmentedViewDelegate
// MARK: - JXSegmentedView 代理 (处理分段菜单的交互)
extension DiscoverViewController: JXSegmentedViewDelegate {
    func segmentedView(_ segmentedView: JXSegmentedView, didSelectedItemAt index: Int) {
        // 当一个item被选中时（包括通过点击、代码选中、滚动结束选中）调用
        if index < menuItems.count { // 确保索引在有效范围内
            let selectedItem = menuItems[index]
            print("分页按钮选中: Name - \(selectedItem.name), ID - \(selectedItem.id)")
        }
        // JXSegmentedView 会自动处理与其关联的 listContainerView 的切换，通常不需要在此处额外代码处理列表切换。
    }
    
    func segmentedView(_ segmentedView: JXSegmentedView, didClickSelectedItemAt index: Int) {
        // 当用户通过点击操作选中一个item时调用
        if index < menuItems.count { // 确保索引在有效范围内
            let clickedItem = menuItems[index]
            // 此回调专用于点击事件。如果 didSelectedItemAt 已处理通用选中逻辑 (包括点击)，
            // 为避免重复打印或处理，此处可以留空，或仅处理特定于"点击"的逻辑。
            // print("分页按钮点击: Name - \(clickedItem.name), ID - \(clickedItem.id)")
        }
        // JXSegmentedView 同样会自动处理列表容器的切换。
    }

    // 以下两个方法是可选实现，如果不需要对滚动过程或重复点击进行特殊处理，可以不实现。
    func segmentedView(_ segmentedView: JXSegmentedView, didScrollSelectedItemAt index: Int) {
        // 可选实现：当通过左右滚动列表容器，导致item被选中时调用此方法。
    }
    
    func segmentedView(_ segmentedView: JXSegmentedView, scrollingFrom leftIndex: Int, to rightIndex: Int, percent: CGFloat) {
        // 可选实现：当列表容器正在滚动中时，此回调会持续触发，可以用于实现一些滚动过程中的过渡动画效果，例如指示器的动态变化。
    }
}

// MARK: - JXSegmentedListContainerViewDataSource
// MARK: - JXSegmentedListContainerView 数据源 (提供列表实例)
extension DiscoverViewController: JXSegmentedListContainerViewDataSource {
    func numberOfLists(in listContainerView: JXSegmentedListContainerView) -> Int {
        // 返回列表的总数量，应与分段菜单的项数一致
        return menuItems.count
    }
    
    func listContainerView(_ listContainerView: JXSegmentedListContainerView, initListAt index: Int) -> JXSegmentedListContainerViewListDelegate {
        let menuItem = menuItems[index]
        if menuItem.name == "推荐" { // 判断名称是否为"推荐"
            let recommendVC = RecommendListViewController()
            recommendVC.delegate = self
            return recommendVC
        } else {
            return NormalListViewController(id: menuItem.id, title: menuItem.name)
        }
    }
}

// MARK: - 自定义渐变色标题单元格 (JXSegmentedTitleCell 子类)
// GradientTitleCell: 自定义 JXSegmentedView 的标题单元格，实现选中时标题文字渐变色效果
class GradientTitleCell: JXSegmentedTitleCell {
    // 用于应用渐变色的 CAGradientLayer
    private var gradientLayer: CAGradientLayer?
    
    override func reloadData(itemModel: JXSegmentedBaseItemModel, selectedType: JXSegmentedViewItemSelectedType) {
        super.reloadData(itemModel: itemModel, selectedType: selectedType)
        
        // 仅在单元格被选中时应用渐变效果
        if itemModel.isSelected {
            applyGradient() // 应用渐变
        } else {
            clearGradient() // 清除渐变 (恢复普通颜色)
        }
    }
    
    // 应用渐变色到标题文本
    private func applyGradient() {
        // 先移除可能已存在的渐变层，以防重复添加
        clearGradient()
        
        // 创建新的 CAGradientLayer实例
        let gradientLayer = CAGradientLayer()
        gradientLayer.frame = titleLabel.bounds // 渐变层大小与标题标签相同
        // 设置渐变色 (从橘红色到橙黄色)
        gradientLayer.colors = [
            UIColor(hex: "#FF5900").cgColor,
            UIColor(hex: "#FF8D36").cgColor
        ]
        gradientLayer.startPoint = CGPoint(x: 0, y: 0.5) // 渐变起始点 (左侧中间)
        gradientLayer.endPoint = CGPoint(x: 1, y: 0.5)   // 渐变结束点 (右侧中间)
        
        // 创建一个与标题文本相同的 CATextLayer 作为渐变层的遮罩 (mask)
        // 这样渐变效果将只应用于文本形状
        let textLayer = CATextLayer()
        textLayer.frame = titleLabel.bounds
        textLayer.string = titleLabel.text // 设置文本内容
        textLayer.font = titleLabel.font // 设置字体 (应与 titleLabel 一致)
        textLayer.fontSize = titleLabel.font.pointSize // 设置字号
        textLayer.alignmentMode = .center // 文本对齐方式
        textLayer.foregroundColor = UIColor.black.cgColor // 遮罩文本的颜色 (实际颜色不重要，形状重要)
        
        // 将文本层设置为渐变层的遮罩
        gradientLayer.mask = textLayer
        
        // 将渐变层添加到标题标签的图层上
        titleLabel.layer.addSublayer(gradientLayer)
        self.gradientLayer = gradientLayer // 保存对渐变层的引用
        
        // 将原始标题标签的文本颜色设为透明，以显示其下的渐变效果
        titleLabel.textColor = .clear
    }
    
    // 清除渐变效果，恢复原始文本颜色
    private func clearGradient() {
        gradientLayer?.removeFromSuperlayer() // 从图层移除渐变层
        gradientLayer = nil // 清空引用
        
        // 恢复标题标签的原始文本颜色 (根据选中状态)
        if let titleModel = itemModel as? JXSegmentedTitleItemModel {
            titleLabel.textColor = titleModel.isSelected ? titleModel.titleSelectedColor : titleModel.titleNormalColor
        }
    }
    
    override func layoutSubviews() {
        super.layoutSubviews()
        
        // 当单元格布局发生变化时 (例如屏幕旋转或父视图大小调整)，更新渐变层及其遮罩的大小
        if let gradientLayer = gradientLayer {
            gradientLayer.frame = titleLabel.bounds
            if let textLayer = gradientLayer.mask as? CATextLayer {
                textLayer.frame = titleLabel.bounds
            }
        }
    }
}

// MARK: - 自定义渐变色标题数据源 (JXSegmentedTitleDataSource 子类)
// GradientTitleDataSource: 自定义 JXSegmentedView 的数据源，用于注册和提供 GradientTitleCell
class GradientTitleDataSource: JXSegmentedTitleDataSource {
    // 重写此方法以注册自定义的 GradientTitleCell
    override func registerCellClass(in segmentedView: JXSegmentedView) {
        segmentedView.collectionView.register(GradientTitleCell.self, forCellWithReuseIdentifier: "GradientTitleCell")
    }
    
    // 重写此方法以在需要时出列并返回 GradientTitleCell 实例
    override func segmentedView(_ segmentedView: JXSegmentedView, cellForItemAt index: Int) -> JXSegmentedBaseCell {
        // 从 collectionView 复用队列中获取 GradientTitleCell
        let cell = segmentedView.collectionView.dequeueReusableCell(withReuseIdentifier: "GradientTitleCell", for: IndexPath(item: index, section: 0)) as! GradientTitleCell
        return cell
    }
}

// MARK: - VideoCollectionCellDelegate
// 在 VideoCollectionCell 中添加代理协议 (声明)
protocol VideoCollectionCellDelegate: AnyObject {
    // 当视频单元格中的某个项目被选中时调用
    func videoCollectionCell(_ cell: VideoCollectionCell, didSelectItemAt index: Int, title: String)
}

// MARK: - RecommendListViewController 实现 VideoCollectionCellDelegate
// 在 RecommendListViewController 中实现 VideoCollectionCellDelegate 代理方法
extension RecommendListViewController: VideoCollectionCellDelegate {
    func videoCollectionCell(_ cell: VideoCollectionCell, didSelectItemAt index: Int, title: String) {
        // TODO: 根据实际业务需要，在此根据 index 和 title 查找对应的 VideoItem（或请求数据）。
        // 目前暂时以列表类型方式进入视频播放页，后续可补充完整的 item 传递。
        let videoVC = VideoDisplayCenterViewController(videoListType: 0)
        videoVC.modalPresentationStyle = .fullScreen // 设置全屏展示
        present(videoVC, animated: true) // 模态弹出视频播放控制器
    }
}

// MARK: - CategorySelectionPopupViewDelegate
// MARK: - 分类弹窗功能代理协议 (CategorySelectionPopupViewDelegate)

protocol CategorySelectionPopupViewDelegate: AnyObject {
    // 当弹窗中的分类被选中时调用
    func categorySelectionPopupView(_ popupView: CategorySelectionPopupView, didSelectItem item: (id: Int, name: String), at popupIndex: Int)
    // 当弹窗请求关闭时调用 (例如点击弹窗头部的关闭按钮)
    func categorySelectionPopupViewDidRequestDismiss(_ popupView: CategorySelectionPopupView)
}


// MARK: - DiscoverViewController 实现 CategorySelectionPopupViewDelegate
// DiscoverViewController 实现 CategorySelectionPopupViewDelegate 代理方法
extension DiscoverViewController: CategorySelectionPopupViewDelegate {
    func categorySelectionPopupView(_ popupView: CategorySelectionPopupView, didSelectItem item: (id: Int, name: String), at popupIndex: Int) {
        // 当在弹窗中选择一个项目时调用
        // 查找选中项在主菜单 menuItems 中的原始索引
        if let originalIndex = self.menuItems.firstIndex(where: { $0.id == item.id && $0.name == item.name }) {
            segmentedView.selectItemAt(index: originalIndex) // 在顶部分段控件中选中对应的项
        }
        dismissCategoryPopup() // 关闭弹窗
    }

    // 实现弹窗请求关闭的方法
    func categorySelectionPopupViewDidRequestDismiss(_ popupView: CategorySelectionPopupView) {
        dismissCategoryPopup() // 调用通用的关闭弹窗方法
    }
}

// MARK: - 商品单元格 (ProductItemCell) 代理协议
// ProductItemCell 操作的新代理协议
protocol ProductItemCellActionDelegate: AnyObject {
    // 当商品单元格中的商品被选中时调用
    func productItemCell(_ cell: ProductItemCell, didSelectProduct product: RecommendedGoodItem)
}

// MARK: - RecommendListViewControllerDelegate
extension DiscoverViewController: RecommendListViewControllerDelegate {
    func recommendListViewController(_ controller: RecommendListViewController, didSelectVideo video: VideoItem, inGroup group: VideoGroup) {
        let videoVC = VideoDisplayCenterViewController(videoItem: video)
        videoVC.modalPresentationStyle = .fullScreen
        controller.present(videoVC, animated: true)
    }
    
    func recommendListViewController(_ controller: RecommendListViewController, didSelectProduct product: RecommendedGoodItem) {
        // 商品跳转逻辑（如有需要可补充）
        // 打印商品信息，便于调试点击前数据
        print("RecommendListViewController: 点击商品 - \(product)")
        
        guard let goodId = product.goodId else {
            print("RecommendListViewControllerDelegate: 商品ID为空，无法跳转")
            return
        }
        let productTitle = product.goodName ?? "商品"
        let productVC = WebViewController(path: "goodPage/goodDetail/goodDetail?id=\(goodId)", title: productTitle)
        productVC.modalPresentationStyle = .fullScreen
        controller.present(productVC, animated: true)
    }
}

// MARK: - 微信小程序跳转扩展
extension DiscoverViewController {
    
    /// 处理微信小程序跳转
    /// - Parameters:
    ///   - userName: 小程序原始ID
    ///   - menuName: 菜单名称（用于日志和提示）
    private func handleWeChatMiniProgramJump(userName: String, menuName: String) {
        print("🚀 准备跳转微信小程序: \(menuName), 小程序ID: \(userName)")

        // 打印环境信息用于调试
        let envInfo = WeChatMiniProgramManager.shared.getEnvironmentInfo()
        print("📱 微信环境信息: \(envInfo)")

        // 检查是否可以跳转
        guard WeChatMiniProgramManager.shared.canLaunchMiniProgram() else {
            let status = WeChatMiniProgramManager.shared.getMiniProgramStatusDescription()
            showToast(status)
            print("❌ 微信小程序跳转失败: \(status)")
            return
        }

        // 显示加载提示
        showToast("正在跳转到\(menuName)...")

        // 执行跳转
        WeChatMiniProgramManager.shared.launchMiniProgram(userName: userName) { [weak self] success, errorMessage in
            DispatchQueue.main.async {
                if success {
                    print("🎉 微信小程序跳转成功: \(menuName)")
                    // 可以在这里添加成功后的处理逻辑，比如埋点统计
                    self?.showToast("跳转成功")
                } else {
                    let error = errorMessage ?? "未知错误"
                    print("❌ 微信小程序跳转失败: \(error)")

                    // 如果是超时错误，给出更友好的提示
                    if error.contains("跳转超时") {
                        self?.showToast("小程序可能已经打开，请检查微信")
                    } else {
                        self?.showToast(error)
                    }
                }
            }
        }
    }
}
