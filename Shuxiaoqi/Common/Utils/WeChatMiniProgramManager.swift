//
//  WeChatMiniProgramManager.swift
//  Shuxiaoqi
//
//  Created by yong<PERSON>ng ye on 2025/1/27.
//

import Foundation
import UIKit

/// 微信小程序跳转管理工具类
class WeChatMiniProgramManager {
    static let shared = WeChatMiniProgramManager()
    private init() {}
    
    /// 跳转到微信小程序
    /// - Parameters:
    ///   - userName: 小程序原始ID，例如：gh_0f1fdf3645e3
    ///   - path: 小程序页面路径（可选）
    ///   - miniProgramType: 小程序类型，默认为正式版
    ///   - completion: 跳转结果回调
    func launchMiniProgram(
        userName: String,
        path: String? = nil,
        miniProgramType: WXMiniProgramType = .release,
        completion: @escaping (Bool, String?) -> Void
    ) {
        // 检查微信是否安装
        guard WXApi.isWXAppInstalled() else {
            completion(false, "微信未安装")
            return
        }

        // 检查微信API是否可用
        guard WXApi.isWXAppSupport() else {
            completion(false, "微信版本过低，不支持小程序功能")
            return
        }

        // 创建小程序跳转请求
        let req = WXLaunchMiniProgramReq()
        req.userName = userName
        req.path = path ?? ""
        req.miniProgramType = miniProgramType

        print("🚀 发送微信小程序跳转请求: \(userName)")

        // 发送请求
        let sendResult = WXApi.send(req)

        // 检查发送是否成功
        guard sendResult else {
            print("❌ 微信小程序跳转请求发送失败")
            completion(false, "跳转请求发送失败")
            return
        }

        print("✅ 微信小程序跳转请求发送成功，等待响应...")

        // 声明观察者变量和超时定时器
        var successObserver: NSObjectProtocol?
        var failureObserver: NSObjectProtocol?
        var timeoutTimer: DispatchWorkItem?
        var hasCompleted = false

        // 创建完成处理闭包，确保只执行一次
        let completeHandler: (Bool, String?) -> Void = { success, errorMessage in
            guard !hasCompleted else { return }
            hasCompleted = true

            // 取消超时定时器
            timeoutTimer?.cancel()

            // 移除观察者
            if let observer = successObserver {
                NotificationCenter.default.removeObserver(observer)
            }
            if let observer = failureObserver {
                NotificationCenter.default.removeObserver(observer)
            }

            // 执行回调
            completion(success, errorMessage)
        }

        // 监听跳转成功
        successObserver = NotificationCenter.default.addObserver(
            forName: .wxMiniProgramLaunchSuccess,
            object: nil,
            queue: .main
        ) { _ in
            print("🎉 收到微信小程序跳转成功通知")
            completeHandler(true, nil)
        }

        // 监听跳转失败
        failureObserver = NotificationCenter.default.addObserver(
            forName: .wxMiniProgramLaunchFailed,
            object: nil,
            queue: .main
        ) { notification in
            let errorMessage = notification.userInfo?["error"] as? String ?? "跳转失败"
            print("❌ 收到微信小程序跳转失败通知: \(errorMessage)")
            completeHandler(false, errorMessage)
        }

        // 设置超时机制 - 延长到10秒，因为微信可能会有多次跳转
        timeoutTimer = DispatchWorkItem {
            print("⏰ 微信小程序跳转超时（10秒）")
            completeHandler(false, "跳转超时；；因为配置问题，微信会回到app在次跳转微信，小程序正常跳转。但是会触发app内屏幕打印跳转超时机制。")
        }

        if let timer = timeoutTimer {
            DispatchQueue.main.asyncAfter(deadline: .now() + 10.0, execute: timer)
        }
    }
    
    /// 跳转到微信小程序（简化版本）
    /// - Parameters:
    ///   - userName: 小程序原始ID
    ///   - completion: 跳转结果回调
    func launchMiniProgram(
        userName: String,
        completion: @escaping (Bool, String?) -> Void
    ) {
        launchMiniProgram(userName: userName, path: nil, completion: completion)
    }
    
    /// 检查是否可以跳转微信小程序
    /// - Returns: 是否可以跳转
    func canLaunchMiniProgram() -> Bool {
        return WXApi.isWXAppInstalled() && WXApi.isWXAppSupport()
    }
    
    /// 获取微信小程序跳转状态描述
    /// - Returns: 状态描述
    func getMiniProgramStatusDescription() -> String {
        if !WXApi.isWXAppInstalled() {
            return "微信未安装"
        }
        if !WXApi.isWXAppSupport() {
            return "微信版本过低"
        }
        return "可以跳转"
    }
    
    /// 测试微信小程序跳转（用于调试）
    /// - Parameter completion: 测试结果回调
    func testMiniProgramLaunch(completion: @escaping (String) -> Void) {
        let testUserName = "gh_0f1fdf3645e3" // 测试小程序ID

        print("开始测试微信小程序跳转...")
        print("微信安装状态: \(WXApi.isWXAppInstalled())")
        print("微信API支持状态: \(WXApi.isWXAppSupport())")

        launchMiniProgram(userName: testUserName) { success, errorMessage in
            let result = success ? "测试成功" : "测试失败: \(errorMessage ?? "未知错误")"
            completion(result)
        }
    }

    /// 处理应用从微信返回的情况
    /// 当应用从微信返回时调用此方法，用于处理可能的配置问题导致的多次跳转
    func handleReturnFromWeChat() {
        print("📱 应用从微信返回，这可能是正常的小程序跳转流程")
        // 这里可以添加一些逻辑来处理从微信返回的情况
        // 比如记录日志、更新UI状态等
    }

    /// 获取详细的微信小程序环境信息（用于调试）
    /// - Returns: 环境信息字典
    func getEnvironmentInfo() -> [String: Any] {
        return [
            "wechatInstalled": WXApi.isWXAppInstalled(),
            "wechatAPISupported": WXApi.isWXAppSupport(),
            "wechatVersion": WXApi.getWXAppInstallUrl() ?? "未知",
            "canLaunch": canLaunchMiniProgram(),
            "statusDescription": getMiniProgramStatusDescription()
        ]
    }
}

// 扩展WXMiniProgramType，提供便利方法
//typedef NS_ENUM(NSUInteger, WXMiniProgramType) {
//    WXMiniProgramTypeRelease = 0,       //**< 正式版  */
//    WXMiniProgramTypeTest = 1,        //**< 开发版  */
//    WXMiniProgramTypePreview = 2,         //**< 体验版  */
//};

