//
//  WeChatMiniProgramManager.swift
//  Shuxia<PERSON>qi
//
//  Created by yong<PERSON>ng ye on 2025/1/27.
//

import Foundation
import UIKit

/// 微信小程序跳转管理工具类
class WeChatMiniProgramManager {
    static let shared = WeChatMiniProgramManager()
    private init() {}
    
    /// 跳转到微信小程序
    /// - Parameters:
    ///   - userName: 小程序原始ID，例如：gh_0f1fdf3645e3
    ///   - path: 小程序页面路径（可选）
    ///   - miniProgramType: 小程序类型，默认为正式版
    ///   - completion: 跳转结果回调
    func launchMiniProgram(
        userName: String,
        path: String? = nil,
        miniProgramType: WXMiniProgramType = .release,
        completion: @escaping (Bool, String?) -> Void
    ) {
        // 检查微信是否安装
        guard WXApi.isWXAppInstalled() else {
            completion(false, "微信未安装")
            return
        }
        
        // 检查微信API是否可用
        guard WXApi.isWXAppSupport() else {
            completion(false, "微信版本过低，不支持小程序功能")
            return
        }
        
        // 创建小程序跳转请求
        let req = WXLaunchMiniProgramReq()
        req.userName = userName
        req.path = path ?? ""
        req.miniProgramType = miniProgramType
        
        // 发送请求
        WXApi.send(req)
        
        // 声明观察者变量
        var successObserver: NSObjectProtocol?
        var failureObserver: NSObjectProtocol?
        
        // 监听跳转结果
        successObserver = NotificationCenter.default.addObserver(
            forName: .wxMiniProgramLaunchSuccess,
            object: nil,
            queue: .main
        ) { _ in
            if let observer = successObserver {
                NotificationCenter.default.removeObserver(observer)
            }
            if let observer = failureObserver {
                NotificationCenter.default.removeObserver(observer)
            }
            completion(true, nil)
        }
        
        failureObserver = NotificationCenter.default.addObserver(
            forName: .wxMiniProgramLaunchFailed,
            object: nil,
            queue: .main
        ) { notification in
            if let observer = successObserver {
                NotificationCenter.default.removeObserver(observer)
            }
            if let observer = failureObserver {
                NotificationCenter.default.removeObserver(observer)
            }
            let errorMessage = notification.userInfo?["error"] as? String ?? "跳转失败"
            completion(false, errorMessage)
        }
        
        // 设置超时，防止回调丢失
        DispatchQueue.main.asyncAfter(deadline: .now() + 5.0) {
            if let observer = successObserver {
                NotificationCenter.default.removeObserver(observer)
            }
            if let observer = failureObserver {
                NotificationCenter.default.removeObserver(observer)
            }
            completion(false, "跳转超时")
        }
    }
    
    /// 跳转到微信小程序（简化版本）
    /// - Parameters:
    ///   - userName: 小程序原始ID
    ///   - completion: 跳转结果回调
    func launchMiniProgram(
        userName: String,
        completion: @escaping (Bool, String?) -> Void
    ) {
        launchMiniProgram(userName: userName, path: nil, completion: completion)
    }
    
    /// 检查是否可以跳转微信小程序
    /// - Returns: 是否可以跳转
    func canLaunchMiniProgram() -> Bool {
        return WXApi.isWXAppInstalled() && WXApi.isWXAppSupport()
    }
    
    /// 获取微信小程序跳转状态描述
    /// - Returns: 状态描述
    func getMiniProgramStatusDescription() -> String {
        if !WXApi.isWXAppInstalled() {
            return "微信未安装"
        }
        if !WXApi.isWXAppSupport() {
            return "微信版本过低"
        }
        return "可以跳转"
    }
    
    /// 测试微信小程序跳转（用于调试）
    /// - Parameter completion: 测试结果回调
    func testMiniProgramLaunch(completion: @escaping (String) -> Void) {
        let testUserName = "gh_0f1fdf3645e3" // 测试小程序ID
        
        print("开始测试微信小程序跳转...")
        print("微信安装状态: \(WXApi.isWXAppInstalled())")
        print("微信API支持状态: \(WXApi.isWXAppSupport())")
        
        launchMiniProgram(userName: testUserName) { success, errorMessage in
            let result = success ? "测试成功" : "测试失败: \(errorMessage ?? "未知错误")"
            completion(result)
        }
    }
}

// 扩展WXMiniProgramType，提供便利方法
//typedef NS_ENUM(NSUInteger, WXMiniProgramType) {
//    WXMiniProgramTypeRelease = 0,       //**< 正式版  */
//    WXMiniProgramTypeTest = 1,        //**< 开发版  */
//    WXMiniProgramTypePreview = 2,         //**< 体验版  */
//};

