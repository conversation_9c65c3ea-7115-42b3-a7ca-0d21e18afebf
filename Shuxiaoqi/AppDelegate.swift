//
//  AppDelegate.swift
//  Shuxiaoqi
//
//  Created by steamed_b on 2025/3/18.
//
//  主题色 #FB6C04

import UIKit
import TXLiteAVSDK_UGC
import IQKeyboardManagerSwift
import ATAuthSDK
import UserNotifications

@main
class AppDelegate: UIResponder, UIApplicationDelegate, WXApiDelegate, JPUSHRegisterDelegate {

    func application(_ application: UIApplication, didFinishLaunchingWithOptions launchOptions: [UIApplication.LaunchOptionsKey: Any]?) -> Bool {
        // Override point for customization after application launch.

        // 关键的UI配置，需要立即执行
        setupCriticalUIConfigurations()

        // 延后执行非关键初始化，避免阻塞启动页
        DispatchQueue.main.async {
            self.setupNonCriticalInitializations(launchOptions: launchOptions)
        }

        return true
    }

    /// 设置关键的UI配置，需要立即执行
    private func setupCriticalUIConfigurations() {
        // 启用 IQKeyboardManager
        IQKeyboardManager.shared.isEnabled = true
        IQKeyboardManager.shared.resignOnTouchOutside = true

        // 设置全局导航栏外观
        let appearance = UINavigationBar.appearance()
        appearance.isHidden = true
        appearance.setBackgroundImage(UIImage(), for: .default)
        appearance.shadowImage = UIImage()
        appearance.isTranslucent = true
        appearance.alpha = 0
    }

    /// 设置非关键初始化，延后执行
    private func setupNonCriticalInitializations(launchOptions: [UIApplication.LaunchOptionsKey: Any]?) {
        // 腾讯云 License 初始化（短视频 & 播放器）
        let licenceURL = "https://license.vod2.myqcloud.com/license/v2/**********_1/v_cube.license"
        let licenceKey = "00439d0788eb79c29ca1642443217418"
        TXUGCBase.setLicenceURL(licenceURL, key: licenceKey)   // UGC（录制/上传）
        TXLiveBase.setLicenceURL(licenceURL, key: licenceKey)  // 播放器 / Live
        TXLiveBase.setLogLevel(.LOGLEVEL_FATAL)
        print(TXUGCBase.getLicenseAppId() as Any)//**********

        //一键登录初始化
        TXCommonHandler.sharedInstance().setAuthSDKInfo("dhIF+uaiDWom2S5gEItbVa2UJTPuNpXhCiRIrY9+wqvQtRFSV9jua2ASUsDq01BHyEqKLXOyq9A00ejnWAg5SWoV8T/1gz9/11qvWISif7QF5kXNfqyDVXkNfBuiUdT+XdH9fr1A63Q7i2JgxhvbvLb3bJDUp1n5F8QF1VjflSKqx5dkzC4WHoC180Bi+JW4d1yUuNU1FTOocNJMhrY47lPkrc8rXGMtjg/g0JKPWiuQhfDLklatFzDGKbsRYJpY") { resultDic in
            print("一键登录的结果：\(resultDic)")
        }

        print("=== 微信SDK注册开始 ===")
        print("AppID: wx92fac135539c31c3")
        print("Universal Link: https://app.gzyoushu.com/")
        print("Bundle ID: \(Bundle.main.bundleIdentifier ?? "未知")")

        if WXApi.registerApp("wx92fac135539c31c3", universalLink: "https://app.gzyoushu.com/") {
            print("✅ 微信SDK注册成功")
            print("微信SDK版本: \(WXApi.getVersion())")
            print("微信是否安装: \(WXApi.isWXAppInstalled())")
            print("微信是否支持API: \(WXApi.isWXAppSupport())")
        } else {
            print("❌ 微信SDK注册失败")
        }
        print("=== 微信SDK注册结束 ===")

        //初始化Jpush
        let entity = JPUSHRegisterEntity()
        entity.types = Int(JPAuthorizationOptions.alert.rawValue | JPAuthorizationOptions.badge.rawValue | JPAuthorizationOptions.sound.rawValue)
        JPUSHService.register(forRemoteNotificationConfig: entity, delegate: self)
        // 设置JPush的AppKey
        JPUSHService.setup(withOption: launchOptions, appKey: "e59c24603c36a00ba3b4ebe8", channel: "默认通道", apsForProduction: false)

        // 延后执行网络请求，避免阻塞启动
        DispatchQueue.global(qos: .background).async {
            self.callFirstEnterWithLocation()
        }
    }

    // MARK: - 设备信息管理

    /// 获取位置信息后调用firstEnter
    private func callFirstEnterWithLocation() {
        print("[firstEnter] 开始执行 callFirstEnterWithLocation 方法")

        let deviceId = UIDevice.current.identifierForVendor?.uuidString ?? ""
        let deviceName = DeviceUtils.marketingDeviceModel()
        let deviceSystem = UIDevice.current.systemVersion
        let deviceType = 1 // iOS设备

        print("[firstEnter] 设备信息 - deviceId: \(deviceId), deviceName: \(deviceName), deviceSystem: \(deviceSystem)")

        // 获取本地IP
        let localIP = DeviceUtils.getCurrentIP()
        print("[firstEnter] 本地IP: \(localIP)")

        // 设置超时机制，确保即使位置获取失败也会调用API
        var hasCalledAPI = false
        let timeoutSeconds: TimeInterval = 8.0

        // 超时回调
        DispatchQueue.main.asyncAfter(deadline: .now() + timeoutSeconds) {
            if !hasCalledAPI {
                print("[firstEnter] 位置获取超时(\(timeoutSeconds)秒)，使用默认值调用API")
                hasCalledAPI = true
                self.callFirstEnterAPI(
                    deviceId: deviceId,
                    deviceName: deviceName,
                    deviceSystem: deviceSystem,
                    deviceType: deviceType,
                    loginAddress: "",
                    ip: localIP
                )
            }
        }

        // 尝试获取位置信息
        print("[firstEnter] 开始获取位置信息...")
        LocationManager.shared.getCurrentAreaCode { [weak self] areaCode, address in
            print("[firstEnter] 位置信息回调 - areaCode: \(areaCode ?? "nil"), address: \(address ?? "nil")")
            let loginAddress = address ?? ""

            // 获取公网IP
            print("[firstEnter] 开始获取公网IP...")
            DeviceUtils.getPublicIP { publicIP in
                print("[firstEnter] 公网IP回调 - publicIP: \(publicIP ?? "nil")")
                let finalIP = publicIP ?? localIP

                // 确保只调用一次API
                if !hasCalledAPI {
                    hasCalledAPI = true
                    self?.callFirstEnterAPI(
                        deviceId: deviceId,
                        deviceName: deviceName,
                        deviceSystem: deviceSystem,
                        deviceType: deviceType,
                        loginAddress: loginAddress,
                        ip: finalIP
                    )
                }
            }
        }
    }

    /// 调用firstEnter API的辅助方法
    private func callFirstEnterAPI(deviceId: String, deviceName: String, deviceSystem: String, deviceType: Int, loginAddress: String, ip: String) {
        // 创建并保存当前设备信息
        let currentDeviceInfo = DeviceUtils.CurrentDeviceInfo(
            deviceId: deviceId,
            deviceName: deviceName,
            deviceSystem: deviceSystem,
            deviceType: deviceType,
            ip: ip,
            location: loginAddress
        )
        DeviceUtils.saveCurrentDeviceInfo(currentDeviceInfo)
        print("[firstEnter] 设备信息已保存")

        // 调用firstEnter接口
        print("[firstEnter] 准备调用 firstEnter API - deviceId: \(deviceId), deviceName: \(deviceName), deviceSystem: \(deviceSystem), deviceType: \(deviceType), loginAddress: \(loginAddress)")

        APIManager.shared.firstEnter(
            deviceId: deviceId,
            deviceName: deviceName,
            deviceSystem: deviceSystem,
            deviceType: deviceType,
            loginAddress: loginAddress
        ) { result in
            print("[firstEnter] API 调用完成，收到回调")
            switch result {
            case .success(let response):
                print("[firstEnter] preload success: \(response.displayMessage)")
            case .failure(let error):
                print("[firstEnter] preload failed: \(error)")
            }
        }
        print("[firstEnter] firstEnter API 调用已发起")
    }

    func application(_ application: UIApplication, continue userActivity: NSUserActivity, restorationHandler: @escaping ([UIUserActivityRestoring]?) -> Void) -> Bool {
        // 检查用户活动的类型是否是 Universal Link
        if userActivity.activityType == NSUserActivityTypeBrowsingWeb {
            if let url = userActivity.webpageURL {
                // 处理打开的 URL（Universal Link）
                print("Received URL: \(url)")
                // 根据 URL 进行页面跳转或其他处理
            }
        }
        WXApi.handleOpenUniversalLink(userActivity, delegate: self)
        return true
    }

    // URL Scheme 回调
    func application(_ app: UIApplication, open url: URL, options: [UIApplication.OpenURLOptionsKey : Any] = [:]) -> Bool {
        print("=== 收到URL Scheme回调 ===")
        print("URL: \(url)")
        print("Options: \(options)")

        let result = WXApi.handleOpen(url, delegate: self)
        print("WXApi处理结果: \(result)")
        return result
    }

    // 兼容老版本
    func application(_ application: UIApplication, handleOpen url: URL) -> Bool {
        print("=== 收到老版本URL回调 ===")
        print("URL: \(url)")

        let result = WXApi.handleOpen(url, delegate: self)
        print("WXApi处理结果: \(result)")
        return result
    }
    

    // MARK: UISceneSession Lifecycle
    
    // AppDelegate.swift
    func onResp(_ resp: BaseResp) {
        switch resp {
        case let auth as SendAuthResp:
            // 处理微信登录响应
            if auth.errCode == WXSuccess.rawValue, let code = auth.code {
                NotificationCenter.default.post(
                    name: .wxLoginCodeReady,
                    object: nil,
                    userInfo: ["code": code]
                )
            } else {
                print("微信登录失败: \(auth.errStr ?? "未知错误")")
            }

        case let shareResp as SendMessageToWXResp:
            // 处理微信分享响应
            print("=== 收到微信分享回调 ===")
            print("错误码: \(shareResp.errCode)")
            print("错误信息: \(shareResp.errStr ?? "无")")
            print("分享类型: \(shareResp.type)")

            if shareResp.errCode == WXSuccess.rawValue {
                print("✅ 微信分享成功")
                DispatchQueue.main.async {
                    // 可以在这里显示分享成功的提示
                    if let windowScene = UIApplication.shared.connectedScenes.first as? UIWindowScene,
                       let window = windowScene.windows.first {
                        let alert = UIAlertController(title: "分享成功", message: "内容已成功分享到微信", preferredStyle: .alert)
                        alert.addAction(UIAlertAction(title: "确定", style: .default))
                        window.rootViewController?.present(alert, animated: true)
                    }
                }
            } else {
                print("❌ 微信分享失败: 错误码=\(shareResp.errCode), 错误信息=\(shareResp.errStr ?? "未知错误")")

                // 解析常见错误码
                let errorMessage: String
                switch shareResp.errCode {
                case -2:
                    errorMessage = "用户取消分享"
                case -3:
                    errorMessage = "发送失败"
                case -4:
                    errorMessage = "授权拒绝"
                case -5:
                    errorMessage = "微信不支持"
                default:
                    errorMessage = shareResp.errStr ?? "分享过程中出现错误"
                }

                DispatchQueue.main.async {
                    // 显示分享失败的提示
                    if let windowScene = UIApplication.shared.connectedScenes.first as? UIWindowScene,
                       let window = windowScene.windows.first {
                        let alert = UIAlertController(title: "分享失败", message: errorMessage, preferredStyle: .alert)
                        alert.addAction(UIAlertAction(title: "确定", style: .default))
                        window.rootViewController?.present(alert, animated: true)
                    }
                }
            }

        case let miniProgram as WXLaunchMiniProgramResp:
            // 处理微信小程序跳转响应
            print("🔄 收到微信小程序响应 - errCode: \(miniProgram.errCode), errStr: \(miniProgram.errStr ?? "无")")

            if miniProgram.errCode == WXSuccess.rawValue {
                print("🎉 微信小程序跳转成功")

                // 通知WeChatMiniProgramManager处理返回
                WeChatMiniProgramManager.shared.handleReturnFromWeChat()

                // 检查是否有支付结果数据
                if let extMsg = miniProgram.extMsg, !extMsg.isEmpty {
                    print("💰 微信小程序返回支付数据: \(extMsg)")
                    // 发送支付结果通知
                    NotificationCenter.default.post(
                        name: .wxMiniProgramPaymentResult,
                        object: nil,
                        userInfo: ["paymentResult": extMsg]
                    )
                } else {
                    // 普通的小程序跳转成功
                    print("✅ 发送小程序跳转成功通知")
                    NotificationCenter.default.post(
                        name: .wxMiniProgramLaunchSuccess,
                        object: nil
                    )
                }
            } else {
                let errorMessage = miniProgram.errStr ?? "未知错误"
                print("❌ 微信小程序跳转失败: \(errorMessage) (错误码: \(miniProgram.errCode))")
                NotificationCenter.default.post(
                    name: .wxMiniProgramLaunchFailed,
                    object: nil,
                    userInfo: ["error": errorMessage]
                )
            }

        default:
            print("收到其他微信响应: \(resp)")
        }
    }
    
    func application(_ application: UIApplication, configurationForConnecting connectingSceneSession: UISceneSession, options: UIScene.ConnectionOptions) -> UISceneConfiguration {
        // Called when a new scene session is being created.
        // Use this method to select a configuration to create the new scene with.
        return UISceneConfiguration(name: "Default Configuration", sessionRole: connectingSceneSession.role)
    }
    
    func application(_ application: UIApplication, didDiscardSceneSessions sceneSessions: Set<UISceneSession>) {
        // Called when the user discards a scene session.
        // If any sessions were discarded while the application was not running, this will be called shortly after application:didFinishLaunchingWithOptions.
        // Use this method to release any resources that were specific to the discarded scenes, as they will not return.
    }

    // MARK: - JPUSHRegisterDelegate
    // iOS 10及以上：前台收到推送
    func jpushNotificationCenter(_ center: UNUserNotificationCenter, willPresent notification: UNNotification, withCompletionHandler completionHandler: @escaping (Int) -> Void) {
        let userInfo = notification.request.content.userInfo
        // 处理推送内容
        print("[JPUSH] willPresent notification: \(userInfo)")
        completionHandler(Int(UNNotificationPresentationOptions.alert.rawValue | UNNotificationPresentationOptions.sound.rawValue | UNNotificationPresentationOptions.badge.rawValue))
    }
    // iOS 10及以上：点击推送
    func jpushNotificationCenter(_ center: UNUserNotificationCenter, didReceive response: UNNotificationResponse, withCompletionHandler completionHandler: @escaping () -> Void) {
        let userInfo = response.notification.request.content.userInfo
        // 处理推送点击
        print("[JPUSH] didReceive response: \(userInfo)")
        completionHandler()
    }
    // iOS 12及以上：通知设置
    func jpushNotificationCenter(_ center: UNUserNotificationCenter, openSettingsFor notification: UNNotification) {
        print("[JPUSH] openSettingsFor notification: \(notification.request.content.userInfo)")
    }
    // 通知授权状态回调
    func jpushNotificationAuthorization(_ status: JPAuthorizationStatus, withInfo info: [AnyHashable : Any]?) {
        print("[JPUSH] Authorization status: \(status), info: \(String(describing: info))")
    }
}

extension Notification.Name {
    static let wxLoginCodeReady = Notification.Name("wxLoginCodeReady")
    static let wxMiniProgramLaunchSuccess = Notification.Name("wxMiniProgramLaunchSuccess")
    static let wxMiniProgramLaunchFailed = Notification.Name("wxMiniProgramLaunchFailed")
    static let wxMiniProgramPaymentResult = Notification.Name("wxMiniProgramPaymentResult")
}
