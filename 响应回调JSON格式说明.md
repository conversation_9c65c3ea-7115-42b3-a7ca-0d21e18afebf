# 响应回调JSON格式说明

## 问题发现

通过WVJB日志发现，响应回调（responseCallback）也需要返回JSON字符串格式，而不是对象格式：

### 问题示例
```
WVJB SEND: {"responseId":"cb_1_1755159205479","responseData":{"status":1,"data":{"lng":"113.33659115380723","lat":"23.14478669931762","address":"中国广东省广州市天河区天河北路689号光大银行大厦505"},"msg":"获取位置成功"}}
```

H5端接收到的是Object，而不是JSON字符串，导致需要额外解析。

## 解决方案

### 1. 创建统一的JSON响应方法
```swift
/// 统一的JSON响应回调方法
private func sendJSONResponse(responseCallback: WVJBResponseCallback?, data: [String: Any], handlerName: String = "unknown") {
    guard let responseCallback = responseCallback else {
        print("【JSON响应】responseCallback为nil，无法发送响应: \(handlerName)")
        return
    }
    
    do {
        // 将数据转换为JSON字符串
        let jsonData = try JSONSerialization.data(withJSONObject: data, options: [])
        guard let jsonString = String(data: jsonData, encoding: .utf8) else {
            print("【JSON响应】JSON字符串转换失败: \(handlerName)")
            responseCallback(["error": "JSON转换失败"])
            return
        }
        
        print("【JSON响应】发送JSON响应 [\(handlerName)]: \(jsonString)")
        
        // 发送JSON字符串作为响应
        responseCallback(jsonString)
        
        print("【JSON响应】已发送JSON响应: \(handlerName)")
    } catch {
        print("【JSON响应】JSON序列化失败 [\(handlerName)]: \(error)")
        responseCallback(["error": "JSON序列化失败: \(error.localizedDescription)"])
    }
}
```

### 2. 修改所有responseCallback调用

#### goLocation（获取位置）
**修改前**:
```swift
let locationResponseCallback: (([String: Any]) -> Void)? = { responseData in
    print("【注册处理】goLocation返回数据: \(responseData)")
    responseCallback?(responseData)
}
```

**修改后**:
```swift
let locationResponseCallback: (([String: Any]) -> Void)? = { responseData in
    print("【注册处理】goLocation返回数据: \(responseData)")
    // 使用统一的JSON响应方法
    self.sendJSONResponse(responseCallback: responseCallback, data: responseData, handlerName: "goLocation")
}
```

#### goCallPhone（拨打电话）
**修改前**:
```swift
responseCallback?(["data": resultData, "status": 1, "msg": "已显示拨打电话界面"])
```

**修改后**:
```swift
let successData = ["data": resultData, "status": 1, "msg": "已显示拨打电话界面"] as [String : Any]
self.sendJSONResponse(responseCallback: responseCallback, data: successData, handlerName: "goCallPhone")
```

#### goGetToken（获取Token）
**修改前**:
```swift
if let callback = responseCallback {
    callback(response)
}
```

**修改后**:
```swift
if let callback = responseCallback {
    sendJSONResponse(responseCallback: callback, data: response, handlerName: "goGetToken")
}
```

#### goHome（回到首页）
**修改前**:
```swift
responseCallback?(["data": NSNull(), "status": 1, "msg": "正在返回首页"])
```

**修改后**:
```swift
let responseData = ["data": NSNull(), "status": 1, "msg": "正在返回首页"] as [String : Any]
self.sendJSONResponse(responseCallback: responseCallback, data: responseData, handlerName: "goHome")
```

## 修改的Handler列表

### 已修改的Handler
1. ✅ **goLocation**: 获取位置
2. ✅ **goCallPhone**: 拨打电话
3. ✅ **goGetToken**: 获取Token
4. ✅ **goHome**: 回到首页
5. ✅ **onPaymentResult**: 支付结果接收
6. ✅ **nativeAction**: 示例Handler
7. ✅ **changeUser**: 示例Handler

### 待修改的Handler
- **goMapPath**: 地图导航
- **goOpenShare**: 分享功能
- **goBackPrevPage**: 返回上一页
- **goScanCode**: 扫一扫（如果存在）

## 数据流程对比

### 修改前
```
iOS: {status: 1, data: {...}} → Bridge → H5: {status: 1, data: {...}}
```

### 修改后
```
iOS: {status: 1, data: {...}} → JSON.stringify → Bridge → H5: '{"status":1,"data":{...}}' → JSON.parse → {status: 1, data: {...}}
```

## WVJB消息格式变化

### 修改前
```json
{
  "responseId": "cb_1_1755159205479",
  "responseData": {
    "status": 1,
    "data": {
      "lng": "113.33659115380723",
      "lat": "23.14478669931762",
      "address": "中国广东省广州市天河区天河北路689号光大银行大厦505"
    },
    "msg": "获取位置成功"
  }
}
```

### 修改后
```json
{
  "responseId": "cb_1_1755159205479",
  "responseData": "{\"status\":1,\"data\":{\"lng\":\"113.33659115380723\",\"lat\":\"23.14478669931762\",\"address\":\"中国广东省广州市天河区天河北路689号光大银行大厦505\"},\"msg\":\"获取位置成功\"}"
}
```

## H5端处理

### JavaScript处理示例
```javascript
bridge.callHandler('goLocation', {isHighAccuracy: true}, function(response) {
    console.log('获取位置原始响应:', response);
    
    // 使用统一的JSON解析函数
    let parsedResponse = parseJSONData(response);
    console.log('获取位置解析响应:', parsedResponse);
    
    // 处理解析后的数据
    if (parsedResponse.status === 1) {
        console.log('位置获取成功:', parsedResponse.data);
    } else {
        console.log('位置获取失败:', parsedResponse.msg);
    }
});
```

## 优势

### 1. 数据一致性
- 所有响应都是JSON字符串格式
- 与主动发送消息的格式保持一致
- 避免了不同数据类型的混乱

### 2. 错误处理
- 统一的JSON序列化错误处理
- 详细的日志记录便于调试
- 降级处理确保稳定性

### 3. 维护性
- 统一的响应处理方法
- 易于添加新的Handler
- 便于问题排查和调试

## 测试验证

### 1. 检查iOS日志
```
【JSON响应】发送JSON响应 [goLocation]: {"status":1,"data":{"lng":"113.33659115380723","lat":"23.14478669931762","address":"中国广东省广州市天河区天河北路689号光大银行大厦505"},"msg":"获取位置成功"}
【JSON响应】已发送JSON响应: goLocation
```

### 2. 检查H5控制台
```
获取位置原始响应: {"status":1,"data":{"lng":"113.33659115380723","lat":"23.14478669931762","address":"中国广东省广州市天河区天河北路689号光大银行大厦505"},"msg":"获取位置成功"}
获取位置解析响应: {status: 1, data: {lng: "113.33659115380723", lat: "23.14478669931762", address: "中国广东省广州市天河区天河北路689号光大银行大厦505"}, msg: "获取位置成功"}
```

### 3. 检查WVJB消息
```
WVJB SEND: {"responseId":"cb_1_1755159205479","responseData":"{\"status\":1,\"data\":{\"lng\":\"113.33659115380723\",\"lat\":\"23.14478669931762\",\"address\":\"中国广东省广州市天河区天河北路689号光大银行大厦505\"},\"msg\":\"获取位置成功\"}"}
```

## 总结

通过统一的JSON响应回调实现，解决了：
1. **响应格式不一致**的问题
2. **H5解析困难**的问题
3. **调试复杂**的问题
4. **维护困难**的问题

现在所有的响应回调都是JSON字符串格式，H5端可以统一解析，确保了数据传输的可靠性和一致性。
