# 完整WVJB消息格式说明

## 修改目标

将WVJB SEND消息的完整结构作为JSON数据发送，而不是只发送data部分。

## 格式对比

### 修改前
**WVJB SEND消息**:
```json
{"data":{"paymentStatus":1},"handlerName":"onPayment"}
```

**H5接收到的data**:
```json
{"paymentStatus":1}
```

### 修改后
**WVJB SEND消息**:
```json
{"data":{"data":{"paymentStatus":1},"handlerName":"onPayment"},"handlerName":"onPayment"}
```

**H5接收到的data**:
```json
{
    "data": {
        "paymentStatus": 1
    },
    "handlerName": "onPayment"
}
```

## 实现方式

### iOS端修改
```swift
// 修改前：只发送paymentStatus
let paymentResult: [String: Any] = [
    "paymentStatus": status
]
bridge.callHandler("onPayment", data: paymentResult)

// 修改后：发送完整的WVJB消息结构
let wvjbMessage: [String: Any] = [
    "data": [
        "paymentStatus": status
    ],
    "handlerName": "onPayment"
]
bridge.callHandler("onPayment", data: wvjbMessage)
```

### H5端处理
```javascript
// 兼容新旧两种格式的处理方式
bridge.registerHandler('onPayment', function(data) {
    console.log('收到支付结果完整消息:', data);
    
    let paymentStatus;
    if (data.data && typeof data.data.paymentStatus !== 'undefined') {
        // 新格式：完整的WVJB消息
        paymentStatus = data.data.paymentStatus;
        console.log('检测到完整WVJB消息格式，handlerName:', data.handlerName);
    } else if (typeof data.paymentStatus !== 'undefined') {
        // 旧格式：只有数据部分
        paymentStatus = data.paymentStatus;
        console.log('检测到简化数据格式');
    } else {
        console.error('未知的支付结果格式:', data);
        paymentStatus = -1;
    }
    
    // 处理支付状态
    switch(paymentStatus) {
        case 0: console.log('支付失败'); break;
        case 1: console.log('支付成功'); break;
        case 2: console.log('取消支付'); break;
        case 3: console.log('未知状态'); break;
    }
});
```

## 消息结构详解

### 完整WVJB消息结构
```json
{
    "data": {                    // 实际的业务数据
        "paymentStatus": 1
    },
    "handlerName": "onPayment"   // 处理器名称
}
```

### 字段说明
- **data**: 包含实际的业务数据（支付状态）
- **handlerName**: 指明这是onPayment处理器的消息
- **paymentStatus**: 支付状态码（0=失败，1=成功，2=取消，3=未知）

## 优势

### 1. 完整性
- 包含了完整的WVJB消息结构
- H5端可以获取到handlerName信息
- 便于调试和日志记录

### 2. 可扩展性
- 可以在data中添加更多字段
- 可以添加消息级别的元数据
- 便于未来功能扩展

### 3. 一致性
- 与WVJB的内部消息格式保持一致
- 便于理解和维护

## 兼容性处理

H5端的处理代码同时支持新旧两种格式：

### 新格式检测
```javascript
if (data.data && typeof data.data.paymentStatus !== 'undefined') {
    // 处理新格式
    paymentStatus = data.data.paymentStatus;
}
```

### 旧格式检测
```javascript
else if (typeof data.paymentStatus !== 'undefined') {
    // 处理旧格式
    paymentStatus = data.paymentStatus;
}
```

## 测试验证

### 1. 检查WVJB日志
应该看到类似的消息：
```
WVJB SEND: {"data":{"data":{"paymentStatus":1},"handlerName":"onPayment"},"handlerName":"onPayment"}
```

### 2. H5控制台输出
```
收到支付结果完整消息: {data: {paymentStatus: 1}, handlerName: "onPayment"}
检测到完整WVJB消息格式，handlerName: onPayment
支付结果通知: 支付成功 (状态码: 1)
```

### 3. 数据完整性
- 确认H5能接收到handlerName字段
- 确认paymentStatus正确解析
- 确认兼容性处理正常工作

## 注意事项

### 1. 消息嵌套
新格式会产生嵌套的data结构，需要注意解析层级。

### 2. 向后兼容
H5端代码需要同时支持新旧格式，确保平滑过渡。

### 3. 调试信息
新格式提供了更丰富的调试信息，便于问题排查。

## 总结

通过将完整的WVJB消息结构作为JSON发送，实现了：
1. **完整性**: 包含了完整的消息结构信息
2. **可扩展性**: 便于添加更多字段和元数据
3. **一致性**: 与WVJB内部格式保持一致
4. **兼容性**: H5端同时支持新旧格式

这种方式让H5端能够获取到完整的WVJB消息信息，包括handlerName等元数据，便于调试和功能扩展。
