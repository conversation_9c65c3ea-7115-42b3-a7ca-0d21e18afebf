//
//  ShuxiaoqiTests.swift
//  ShuxiaoqiTests
//
//  Created by steamed_b on 2025/3/18.
//

import XCTest
@testable import Shuxiaoqi

final class ShuxiaoqiTests: XCTestCase {

    override func setUpWithError() throws {
        // Put setup code here. This method is called before the invocation of each test method in the class.
    }

    override func tearDownWithError() throws {
        // Put teardown code here. This method is called after the invocation of each test method in the class.
    }

    func testExample() throws {
        // This is an example of a functional test case.
        // Use XCTAssert and related functions to verify your tests produce the correct results.
        // Any test you write for XCTest can be annotated as throws and async.
        // Mark your test throws to produce an unexpected failure when your test encounters an uncaught error.
        // Mark your test async to allow awaiting for asynchronous code to complete. Check the results with assertions afterwards.
    }

    func testWeChatMiniProgramManagerEnvironmentInfo() throws {
        // Test that the environment info method returns expected data structure
        let envInfo = WeChatMiniProgramManager.shared.getEnvironmentInfo()

        // Verify all expected keys are present
        XCTAssertNotNil(envInfo["wechatInstalled"])
        XCTAssertNotNil(envInfo["wechatAPISupported"])
        XCTAssertNotNil(envInfo["wechatVersion"])
        XCTAssertNotNil(envInfo["canLaunch"])
        XCTAssertNotNil(envInfo["statusDescription"])

        // Verify data types
        XCTAssertTrue(envInfo["wechatInstalled"] is Bool)
        XCTAssertTrue(envInfo["wechatAPISupported"] is Bool)
        XCTAssertTrue(envInfo["canLaunch"] is Bool)
        XCTAssertTrue(envInfo["statusDescription"] is String)
    }

    func testWeChatMiniProgramManagerStatusDescription() throws {
        // Test that status description returns valid strings
        let status = WeChatMiniProgramManager.shared.getMiniProgramStatusDescription()
        XCTAssertFalse(status.isEmpty)

        // Should be one of the expected status messages
        let validStatuses = ["微信未安装", "微信版本过低", "可以跳转"]
        XCTAssertTrue(validStatuses.contains(status))
    }

    func testPerformanceExample() throws {
        // This is an example of a performance test case.
        self.measure {
            // Put the code you want to measure the time of here.
        }
    }

}
