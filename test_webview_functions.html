<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WebView功能测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            text-align: center;
            color: #333;
            margin-bottom: 30px;
        }
        .test-section {
            margin-bottom: 30px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        .test-section h3 {
            margin-top: 0;
            color: #666;
        }
        button {
            background-color: #007AFF;
            color: white;
            border: none;
            padding: 12px 20px;
            margin: 5px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 16px;
            min-width: 120px;
        }
        button:hover {
            background-color: #0056CC;
        }
        .result {
            margin-top: 10px;
            padding: 10px;
            background-color: #f8f9fa;
            border-radius: 4px;
            font-family: monospace;
            font-size: 14px;
        }
        input[type="text"] {
            width: 100%;
            padding: 8px;
            margin: 5px 0;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>WebView功能测试页面</h1>
        
        <!-- 支付功能测试 -->
        <div class="test-section">
            <h3>支付功能测试 (goPos)</h3>
            <button onclick="testWechatPay()">测试微信支付</button>
            <button onclick="testAlipay()">测试支付宝支付</button>
            <div id="payResult" class="result"></div>
        </div>
        
        <!-- 导航功能测试 -->
        <div class="test-section">
            <h3>导航功能测试</h3>
            <button onclick="testGoHome()">回到首页 (goHome)</button>
            <button onclick="testGoBack()">返回上一页 (goBackPrevPage)</button>
            <div id="navResult" class="result"></div>
        </div>
        
        <!-- 地图导航测试 -->
        <div class="test-section">
            <h3>地图导航测试 (goMapPath)</h3>
            <input type="text" id="latitude" placeholder="纬度 (例如: 23.1291)" value="23.1291">
            <input type="text" id="longitude" placeholder="经度 (例如: 113.2644)" value="113.2644">
            <input type="text" id="locationName" placeholder="地点名称 (例如: 广州塔)" value="广州塔">
            <button onclick="testMapNavigation()">打开地图导航</button>
            <div id="mapResult" class="result"></div>
        </div>
        
        <!-- 分享功能测试 -->
        <div class="test-section">
            <h3>分享功能测试 (goOpenShare)</h3>
            <input type="text" id="shareTitle" placeholder="分享标题" value="树小柒精彩内容">
            <input type="text" id="sharePath" placeholder="分享链接" value="https://www.shuxiaoqi.com">
            <input type="text" id="shareImage" placeholder="分享图片URL" value="">
            <button onclick="testShare()">打开分享</button>
            <div id="shareResult" class="result"></div>
        </div>
        
        <!-- 其他功能测试 -->
        <div class="test-section">
            <h3>其他功能测试</h3>
            <button onclick="testGetToken()">获取Token (goGetToken)</button>
            <button onclick="testLogin()">登录 (goLogin)</button>
            <button onclick="testLocation()">获取位置 (goLocation)</button>
            <button onclick="testCallPhone()">拨打电话 (goCallPhone)</button>
            <button onclick="testScanCode()">扫一扫 (goScanCode)</button>
            <div id="otherResult" class="result"></div>
        </div>
    </div>

    <script>
        // 统一的JSON数据解析函数
        function parseJSONData(data) {
            try {
                // 如果data是字符串，尝试解析为JSON
                if (typeof data === 'string') {
                    return JSON.parse(data);
                }
                // 如果data已经是对象，直接返回
                else if (typeof data === 'object' && data !== null) {
                    return data;
                }
                // 其他情况返回空对象
                else {
                    console.warn('无法解析的数据类型:', typeof data, data);
                    return {};
                }
            } catch (error) {
                console.error('JSON解析失败:', error, '原始数据:', data);
                return {};
            }
        }

        // 初始化WebViewJavascriptBridge
        function setupWebViewJavascriptBridge(callback) {
            if (window.WebViewJavascriptBridge) {
                return callback(WebViewJavascriptBridge);
            }
            if (window.WVJBCallbacks) {
                return window.WVJBCallbacks.push(callback);
            }
            window.WVJBCallbacks = [callback];
            var WVJBIframe = document.createElement('iframe');
            WVJBIframe.style.display = 'none';
            WVJBIframe.src = 'https://__bridge_loaded__';
            document.documentElement.appendChild(WVJBIframe);
            setTimeout(function() {
                document.documentElement.removeChild(WVJBIframe);
            }, 0);
        }

        // 初始化bridge
        setupWebViewJavascriptBridge(function(bridge) {
            // 注册支付结果回调
            bridge.registerHandler('onPayment', function(data) {
                console.log('收到支付结果原始数据:', data);

                // 统一的JSON解析处理
                let parsedData = parseJSONData(data);
                console.log('解析后的支付结果:', parsedData);

                let paymentStatus = parsedData.paymentStatus || -1;
                let statusText = '';
                switch(paymentStatus) {
                    case 0: statusText = '支付失败'; break;
                    case 1: statusText = '支付成功'; break;
                    case 2: statusText = '取消支付'; break;
                    case 3: statusText = '未知状态'; break;
                    default: statusText = '无效状态';
                }

                document.getElementById('payResult').innerHTML =
                    '支付结果通知: ' + statusText + ' (状态码: ' + paymentStatus + ')<br>' +
                    '原始数据: ' + JSON.stringify(data, null, 2) + '<br>' +
                    '解析数据: ' + JSON.stringify(parsedData, null, 2);
            });
            
            // 注册token保存回调
            bridge.registerHandler('onSaveToken', function(data) {
                console.log('收到token原始数据:', data);
                let parsedData = parseJSONData(data);
                console.log('解析后的token数据:', parsedData);
                document.getElementById('otherResult').innerHTML =
                    '收到Token:<br>' +
                    '原始数据: ' + JSON.stringify(data, null, 2) + '<br>' +
                    '解析数据: ' + JSON.stringify(parsedData, null, 2);
            });

            // 注册bridge连通性测试回调
            bridge.registerHandler('onTest', function(data) {
                console.log('收到bridge测试原始数据:', data);
                let parsedData = parseJSONData(data);
                console.log('解析后的测试数据:', parsedData);
                document.getElementById('otherResult').innerHTML =
                    'Bridge测试:<br>' +
                    '原始数据: ' + JSON.stringify(data, null, 2) + '<br>' +
                    '解析数据: ' + JSON.stringify(parsedData, null, 2);
                return {"status": "ok", "message": "bridge连通正常"};
            });
        });

        // 测试微信支付（小程序方式）
        function testWechatPay() {
            const payData = {
                orderDetail: {
                    type: "APPWX"
                },
                // 模拟完整的支付数据，这些数据会被传递给微信小程序
                payInfo: {
                    orderId: "test_order_123",
                    amount: "0.01",
                    description: "测试商品",
                    userId: "test_user_456"
                },
                // 添加其他可能需要的字段
                timestamp: Math.floor(Date.now() / 1000),
                merchantId: "test_merchant"
            };

            setupWebViewJavascriptBridge(function(bridge) {
                bridge.callHandler('goPos', payData, function(response) {
                    console.log('微信支付响应:', response);
                    document.getElementById('payResult').innerHTML = '微信支付响应: ' + JSON.stringify(response, null, 2);
                });
            });
        }

        // 测试支付宝支付
        function testAlipay() {
            const payData = {
                orderDetail: {
                    type: "APPZFB"
                },
                payInfo: "https://qr.alipay.com/bax00615hkuenze9s2ga0035"
            };

            setupWebViewJavascriptBridge(function(bridge) {
                bridge.callHandler('goPos', payData, function(response) {
                    console.log('支付宝支付响应:', response);
                    document.getElementById('payResult').innerHTML = '支付宝支付响应: ' + JSON.stringify(response, null, 2);
                });
            });
        }

        // 测试回到首页
        function testGoHome() {
            setupWebViewJavascriptBridge(function(bridge) {
                bridge.callHandler('goHome', {}, function(response) {
                    console.log('回到首页响应:', response);
                    document.getElementById('navResult').innerHTML = '回到首页响应: ' + JSON.stringify(response, null, 2);
                });
            });
        }

        // 测试返回上一页
        function testGoBack() {
            setupWebViewJavascriptBridge(function(bridge) {
                bridge.callHandler('goBackPrevPage', {}, function(response) {
                    console.log('返回上一页响应:', response);
                    document.getElementById('navResult').innerHTML = '返回上一页响应: ' + JSON.stringify(response, null, 2);
                });
            });
        }

        // 测试地图导航
        function testMapNavigation() {
            const lat = document.getElementById('latitude').value;
            const lng = document.getElementById('longitude').value;
            const name = document.getElementById('locationName').value;
            
            const mapData = {
                lat: lat,
                lng: lng,
                name: name
            };
            
            setupWebViewJavascriptBridge(function(bridge) {
                bridge.callHandler('goMapPath', mapData, function(response) {
                    console.log('地图导航响应:', response);
                    document.getElementById('mapResult').innerHTML = '地图导航响应: ' + JSON.stringify(response, null, 2);
                });
            });
        }

        // 测试分享
        function testShare() {
            const title = document.getElementById('shareTitle').value;
            const path = document.getElementById('sharePath').value;
            const imageUrl = document.getElementById('shareImage').value;
            
            const shareData = {
                title: title,
                path: path,
                imageUrl: imageUrl
            };
            
            setupWebViewJavascriptBridge(function(bridge) {
                bridge.callHandler('goOpenShare', shareData, function(response) {
                    console.log('分享响应:', response);
                    document.getElementById('shareResult').innerHTML = '分享响应: ' + JSON.stringify(response, null, 2);
                });
            });
        }

        // 测试获取Token
        function testGetToken() {
            setupWebViewJavascriptBridge(function(bridge) {
                bridge.callHandler('goGetToken', {}, function(response) {
                    console.log('获取Token响应:', response);
                    document.getElementById('otherResult').innerHTML = '获取Token响应: ' + JSON.stringify(response, null, 2);
                });
            });
        }

        // 测试登录
        function testLogin() {
            setupWebViewJavascriptBridge(function(bridge) {
                bridge.callHandler('goLogin', {}, function(response) {
                    console.log('登录响应:', response);
                    document.getElementById('otherResult').innerHTML = '登录响应: ' + JSON.stringify(response, null, 2);
                });
            });
        }

        // 测试获取位置
        function testLocation() {
            setupWebViewJavascriptBridge(function(bridge) {
                bridge.callHandler('goLocation', {isHighAccuracy: true}, function(response) {
                    console.log('获取位置原始响应:', response);
                    // 解析JSON响应
                    let parsedResponse = parseJSONData(response);
                    console.log('获取位置解析响应:', parsedResponse);

                    // 格式化显示位置信息
                    let locationInfo = '';
                    if (parsedResponse.status === 1 && parsedResponse.data) {
                        const data = parsedResponse.data;
                        locationInfo =
                            '<h4>位置信息详情:</h4>' +
                            '<p><strong>纬度:</strong> ' + (data.latitude || 'N/A') + '</p>' +
                            '<p><strong>经度:</strong> ' + (data.longitude || 'N/A') + '</p>' +
                            '<p><strong>完整地址:</strong> ' + (data.address || 'N/A') + '</p>' +
                            '<p><strong>省份:</strong> ' + (data.province || 'N/A') + '</p>' +
                            '<p><strong>城市:</strong> ' + (data.city || 'N/A') + '</p>' +
                            '<p><strong>区县:</strong> ' + (data.district || 'N/A') + '</p>' +
                            '<p><strong>街道地址:</strong> ' + (data.name || 'N/A') + '</p>';
                    } else {
                        locationInfo = '<p style="color: red;">位置获取失败: ' + (parsedResponse.msg || '未知错误') + '</p>';
                    }

                    document.getElementById('otherResult').innerHTML =
                        locationInfo + '<br>' +
                        '<details><summary>原始数据</summary><pre>' + JSON.stringify(response, null, 2) + '</pre></details>' +
                        '<details><summary>解析数据</summary><pre>' + JSON.stringify(parsedResponse, null, 2) + '</pre></details>';
                });
            });
        }

        // 测试拨打电话
        function testCallPhone() {
            setupWebViewJavascriptBridge(function(bridge) {
                bridge.callHandler('goCallPhone', {phone: "10086"}, function(response) {
                    console.log('拨打电话响应:', response);
                    document.getElementById('otherResult').innerHTML = '拨打电话响应: ' + JSON.stringify(response, null, 2);
                });
            });
        }

        // 测试扫一扫
        function testScanCode() {
            setupWebViewJavascriptBridge(function(bridge) {
                bridge.callHandler('goScanCode', {}, function(response) {
                    console.log('扫一扫响应:', response);
                    document.getElementById('otherResult').innerHTML = '扫一扫响应: ' + JSON.stringify(response, null, 2);
                });
            });
        }
    </script>
</body>
</html>
