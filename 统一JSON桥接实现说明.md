# 统一JSON桥接实现说明

## 实现目标

将所有的桥接函数都改为传递JSON字符串格式，H5端统一解析JSON，避免数据格式不一致导致的错误。

## 核心实现

### 1. iOS端统一JSON发送方法
```swift
/// 统一的JSON桥接发送方法
private func sendJSONToBridge(handlerName: String, data: [String: Any]) {
    guard let bridge = bridge else {
        print("【JSON桥接】Bridge未初始化，无法发送数据到: \(handlerName)")
        return
    }
    
    do {
        // 将数据转换为JSON字符串
        let jsonData = try JSONSerialization.data(withJSONObject: data, options: [])
        guard let jsonString = String(data: jsonData, encoding: .utf8) else {
            print("【JSON桥接】JSON字符串转换失败: \(handlerName)")
            return
        }
        
        print("【JSON桥接】发送JSON到H5 [\(handlerName)]: \(jsonString)")
        
        // 发送JSON字符串到H5
        bridge.callHandler(handlerName, data: jsonString)
        
        print("【JSON桥接】已发送JSON到H5: \(handlerName)")
    } catch {
        print("【JSON桥接】JSON序列化失败 [\(handlerName)]: \(error)")
    }
}
```

### 2. H5端统一JSON解析函数
```javascript
// 统一的JSON数据解析函数
function parseJSONData(data) {
    try {
        // 如果data是字符串，尝试解析为JSON
        if (typeof data === 'string') {
            return JSON.parse(data);
        }
        // 如果data已经是对象，直接返回
        else if (typeof data === 'object' && data !== null) {
            return data;
        }
        // 其他情况返回空对象
        else {
            console.warn('无法解析的数据类型:', typeof data, data);
            return {};
        }
    } catch (error) {
        console.error('JSON解析失败:', error, '原始数据:', data);
        return {};
    }
}
```

## 修改的桥接函数

### 1. 支付结果通知 (onPayment)
**修改前**:
```swift
let paymentResult = ["paymentStatus": status]
bridge.callHandler("onPayment", data: paymentResult)
```

**修改后**:
```swift
let paymentData = ["paymentStatus": status]
sendJSONToBridge(handlerName: "onPayment", data: paymentData)
```

### 2. Token保存通知 (onSaveToken)
**修改前**:
```swift
bridge.callHandler("onSaveToken", data: ["token": token], responseCallback: nil)
```

**修改后**:
```swift
let tokenData = ["token": token]
sendJSONToBridge(handlerName: "onSaveToken", data: tokenData)
```

### 3. Bridge连通性测试 (onTest)
**修改前**:
```swift
let testData = ["test": "bridge_connectivity", "timestamp": Date().timeIntervalSince1970]
bridge.callHandler("onTest", data: testData)
```

**修改后**:
```swift
let testData = ["test": "bridge_connectivity", "timestamp": Date().timeIntervalSince1970]
sendJSONToBridge(handlerName: "onTest", data: testData)
```

### 4. 通用消息发送 (sendMessageToJavaScript)
**修改后**:
```swift
func sendMessageToJavaScript(handlerName: String, data: Any?) {
    if let dataDict = data as? [String: Any] {
        sendJSONToBridge(handlerName: handlerName, data: dataDict)
    } else if let dataString = data as? String {
        bridge.callHandler(handlerName, data: dataString)
    } else if data == nil {
        sendJSONToBridge(handlerName: handlerName, data: [:])
    } else {
        print("【桥接】不支持的数据类型: \(type(of: data))")
    }
}
```

## H5端处理示例

### 1. 支付结果处理
```javascript
bridge.registerHandler('onPayment', function(data) {
    console.log('收到支付结果原始数据:', data);
    
    // 统一的JSON解析处理
    let parsedData = parseJSONData(data);
    console.log('解析后的支付结果:', parsedData);
    
    let paymentStatus = parsedData.paymentStatus || -1;
    // 处理支付状态...
});
```

### 2. Token处理
```javascript
bridge.registerHandler('onSaveToken', function(data) {
    console.log('收到token原始数据:', data);
    let parsedData = parseJSONData(data);
    console.log('解析后的token数据:', parsedData);
    
    let token = parsedData.token;
    // 处理token...
});
```

### 3. 测试消息处理
```javascript
bridge.registerHandler('onTest', function(data) {
    console.log('收到bridge测试原始数据:', data);
    let parsedData = parseJSONData(data);
    console.log('解析后的测试数据:', parsedData);
    
    return {"status": "ok", "message": "bridge连通正常"};
});
```

## 数据流程对比

### 修改前
```
iOS: {paymentStatus: 1} → Bridge → H5: {paymentStatus: 1}
```

### 修改后
```
iOS: {paymentStatus: 1} → JSON.stringify → Bridge → H5: '{"paymentStatus":1}' → JSON.parse → {paymentStatus: 1}
```

## 优势

### 1. 数据一致性
- 所有桥接数据都是JSON字符串格式
- 避免了不同数据类型导致的解析错误
- 统一的数据处理流程

### 2. 错误处理
- 统一的JSON解析错误处理
- 详细的日志记录便于调试
- 兼容性处理支持新旧格式

### 3. 可扩展性
- 易于添加新的桥接函数
- 统一的数据格式便于维护
- 支持复杂的数据结构

### 4. 调试友好
- 详细的日志输出
- 原始数据和解析数据都可见
- 便于问题排查

## 兼容性处理

H5端的parseJSONData函数同时支持：
- **JSON字符串**: 自动解析为对象
- **对象数据**: 直接返回（向后兼容）
- **其他类型**: 返回空对象并记录警告

## 测试验证

### 1. 检查iOS日志
```
【JSON桥接】发送JSON到H5 [onPayment]: {"paymentStatus":1}
【JSON桥接】已发送JSON到H5: onPayment
```

### 2. 检查H5控制台
```
收到支付结果原始数据: {"paymentStatus":1}
解析后的支付结果: {paymentStatus: 1}
```

### 3. 检查WVJB消息
```
WVJB SEND: {"data":"{\"paymentStatus\":1}","handlerName":"onPayment"}
```

## 注意事项

### 1. JSON序列化
- 确保数据可以被JSON序列化
- 避免循环引用等问题
- 处理特殊数据类型（Date、函数等）

### 2. 错误处理
- JSON解析失败时的降级处理
- 网络异常时的重试机制
- 数据格式验证

### 3. 性能考虑
- JSON序列化/反序列化的性能开销
- 大数据量时的处理策略
- 内存使用优化

## 总结

通过统一的JSON桥接实现，解决了：
1. **数据格式不一致**的问题
2. **H5解析错误**的问题
3. **调试困难**的问题
4. **维护复杂**的问题

现在所有的桥接数据都是JSON字符串格式，H5端统一解析，确保了数据传输的可靠性和一致性。
